//package com.vos.kernel.xxljob.jobs;
//
//import com.vos.kernel.data.api.rpc.TaskService;
//import com.xxl.job.core.biz.model.ReturnT;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.scheduling.annotation.EnableAsync;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.time.LocalDateTime;
//
//@Component
//@Slf4j
//@EnableAsync(proxyTargetClass=true)
//public class CheckTaskJob {
//
//
//    @Resource
//    TaskService taskService;
//    /**
//     * 58s
//     */
////    @Scheduled(cron = "58 * * * * *")
//    @XxlJob(value = "checkTaskStart")
//    public ReturnT<String> checkTaskStart() {
//        log.info("检测checkTaskStart{}", LocalDateTime.now());
//        taskService.checkVideoTaskTimeStart();
//        return ReturnT.SUCCESS;
//    }
//
//    /**
//     * 2s
//     */
////    @Scheduled(cron = "2 * * * * *")
//    @XxlJob(value = "checkTaskEnd")
//    public ReturnT<String> checkTaskEnd() {
//        log.info("检测checkTaskEnd", LocalDateTime.now());
//        taskService.checkVideoTaskTimeEnd();
//        return ReturnT.SUCCESS;
//    }
//}
