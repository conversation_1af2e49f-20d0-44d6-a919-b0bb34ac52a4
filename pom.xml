<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>linker-parent</artifactId>
        <groupId>com.linker.parent</groupId>
        <version>1.0.4</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.vos.kernel</groupId>
    <artifactId>vos-kernel</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <build>
        <!-- mvn versions:set -DnewVersion=1.1.9.7 versions:commit -->
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.2.0</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
            </plugin>
        </plugins>
    </build>

    <modules>
        <module>vos-kernel-core</module>
        <module>vos-task-manage</module>
        <module>vos-task-poll</module>
        <module>vos-task-automated</module>
        <module>vos-kernel-data</module>
        <module>vos-kernel-common</module>
        <module>vos-kernel-business</module>
        <module>vos-kernel-output</module>
        <module>vos-image-upload</module>
    </modules>

    <properties>
        <dubbo.version>3.2.9</dubbo.version>
        <hutool.version>5.8.5</hutool.version>
        <retrofit.version>2.3.11</retrofit.version>
        <retrofit2.version>2.9.0</retrofit2.version>
        <linker-basic.version>1.0.4-SNAPSHOT</linker-basic.version>
        <internal.module.version>1.0.0-SNAPSHOT</internal.module.version>
        <rocketmq.version>4.9.3</rocketmq.version>
        <rocketmq-starter.version>2.2.2</rocketmq-starter.version>
        <redis.clients.version>5.0.0</redis.clients.version>
        <redisson.version>3.17.6</redisson.version>
        <mybatis-plus.version>3.5.2</mybatis-plus.version>
        <freemarker.version>2.3.30</freemarker.version>
        <fastjson2.version>2.0.37</fastjson2.version>
        <fastjson.version>1.2.83</fastjson.version>
        <knife4j.version>2.0.9</knife4j.version>
        <dynamic-thread-pool.version>1.1.5</dynamic-thread-pool.version>
        <micrometer.version>1.8.0</micrometer.version>
        <nacos.version>2.1.2</nacos.version>
        <druid.version>1.2.14</druid.version>
        <transmittable.version>2.14.2</transmittable.version>
        <seata.version>1.4.2</seata.version>
        <skywalking-agent.version>8.13.0</skywalking-agent.version>
        <kubernetes-client.version>16.0.2</kubernetes-client.version>
        <curator.version>5.4.0</curator.version>
        <jacoco.version>0.8.8</jacoco.version>
        <minio.version>8.5.2</minio.version>
        <webdav.version>5.10</webdav.version>
        <aliyun.version>3.15.1</aliyun.version>
        <aws.version>1.12.464</aws.version>
        <easyexcel.version>2.2.10</easyexcel.version>
        <jetcache.version>2.7.3</jetcache.version>
        <jetcache-jedis.version>2.7.5</jetcache-jedis.version>
        <joor.version>0.9.6</joor.version>
        <protobuf_java.version>3.23.2</protobuf_java.version>
        <okhttp.version>4.11.0</okhttp.version>
        <disruptor.version>3.4.3</disruptor.version>
        <agrona.version>1.21.2</agrona.version>
        <ohc.version>0.7.4</ohc.version>
        <protostuff.version>1.6.0</protostuff.version>
    </properties>
    <!-- 统一依赖管理 -->
    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>org.agrona</groupId>
                <artifactId>agrona</artifactId>
                <version>${agrona.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-starter-redis</artifactId>
                <version>2.7.5</version>
            </dependency>

            <dependency>
                <groupId>com.linker.basic</groupId>
                <artifactId>linker-basic</artifactId>
                <version>${linker-basic.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba.cloud</groupId>
                        <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>druid-spring-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.mybatis.spring.boot</groupId>
                        <artifactId>mybatis-spring-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.redisson</groupId>
                        <artifactId>redisson-spring-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.linker</groupId>
                <artifactId>core-utils</artifactId>
                <version>1.0.4-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp-sse</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp-brotli</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jooq</groupId>
                <artifactId>joor</artifactId>
                <version>${joor.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf_java.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-starter-redis-lettuce</artifactId>
                <version>${jetcache.version}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>io.kubernetes</groupId>
                <artifactId>client-java</artifactId>
                <version>${kubernetes-client.version}</version>
            </dependency>

            <dependency>
                <groupId>io.kubernetes</groupId>
                <artifactId>client-java-extended</artifactId>
                <version>${kubernetes-client.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>${skywalking-agent.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-trace</artifactId>
                <version>${skywalking-agent.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-webflux</artifactId>
                <version>${skywalking-agent.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>druid</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>

            <dependency>
                <groupId>com.vos.kernel</groupId>
                <artifactId>common-base</artifactId>
                <version>${internal.module.version}</version>
            </dependency>


            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
                <version>2021.1</version>
            </dependency>

            <!--  sentinel规则持久化-->
            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-datasource-nacos</artifactId>
                <version>1.8.6</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${nacos.version}</version>
            </dependency>

            <!--dubbo RPC-->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo</artifactId>
                <version>${dubbo.version}</version>
            </dependency>

            <!--kryo序列化-->
            <dependency>
                <groupId>org.apache.dubbo.extensions</groupId>
                <artifactId>dubbo-serialization-kryo</artifactId>
                <version>1.0.1</version>
            </dependency>

            <dependency>
                <groupId>com.esotericsoftware</groupId>
                <artifactId>kryo</artifactId>
                <version>5.4.0</version>
            </dependency>
            <dependency>
                <groupId>de.javakaffee</groupId>
                <artifactId>kryo-serializers</artifactId>
                <version>0.45</version>
            </dependency>

            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-registry-nacos</artifactId>
                <version>${dubbo.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.dubbo</groupId>
                        <artifactId>dubbo-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.dubbo</groupId>
                        <artifactId>dubbo-remoting-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.dubbo.extensions</groupId>
                <artifactId>dubbo-cluster-specify-address-dubbo3</artifactId>
                <version>1.0.0</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-core</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-crypto</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!--糊涂工具类扩展-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-extra</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.20</version>
                <scope>provided</scope>
            </dependency>

            <!--retrofit-->
            <dependency>
                <groupId>com.github.lianjiatech</groupId>
                <artifactId>retrofit-spring-boot-starter</artifactId>
                <version>${retrofit.version}</version>
            </dependency>


            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>converter-gson</artifactId>
                <version>${retrofit2.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>converter-protobuf</artifactId>
                <version>${retrofit2.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>adapter-rxjava2</artifactId>
                <version>${retrofit2.version}</version>
            </dependency>


            <!--mybatis-plus代码生成器-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!--动态线程池-->
            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-cloud-starter-nacos</artifactId>
                <version>${dynamic-thread-pool.version}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-adapter-webserver</artifactId>
                <version>${dynamic-thread-pool.version}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-extension-skywalking</artifactId>
                <version>${dynamic-thread-pool.version}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-adapter-dubbo</artifactId>
                <version>${dynamic-thread-pool.version}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara.dynamictp</groupId>
                <artifactId>dynamic-tp-spring-boot-starter-adapter-rocketmq</artifactId>
                <version>${dynamic-thread-pool.version}</version>
            </dependency>

            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-prometheus</artifactId>
                <version>${micrometer.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!--模块依赖-->
            <dependency>
                <groupId>com.vos.kernel</groupId>
                <artifactId>vos-kernel-common</artifactId>
                <version>${internal.module.version}</version>
            </dependency>

            <dependency>
                <groupId>com.vos.kernel.core</groupId>
                <artifactId>vos-kernel-core-api</artifactId>
                <version>${internal.module.version}</version>
            </dependency>

            <dependency>
                <groupId>com.vos.task.manage</groupId>
                <artifactId>vos-task-manage-api</artifactId>
                <version>${internal.module.version}</version>
            </dependency>

            <dependency>
                <groupId>com.vos.kernel.data</groupId>
                <artifactId>vos-kernel-data-api</artifactId>
                <version>${internal.module.version}</version>
            </dependency>

            <dependency>
                <groupId>com.vos.task.automated</groupId>
                <artifactId>vos-task-automated-api</artifactId>
                <version>${internal.module.version}</version>
            </dependency>

            <dependency>
                <groupId>com.vos.task.manage</groupId>
                <artifactId>vos-task-manage-service</artifactId>
                <version>${internal.module.version}</version>
            </dependency>

            <!--xxl-job-starter-->
            <dependency>
                <groupId>com.vos.kernel.common</groupId>
                <artifactId>xxl-job-starter</artifactId>
                <version>${internal.module.version}</version>
            </dependency>


            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- rocketmq -->
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-client</artifactId>
                <version>${rocketmq.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-acl</artifactId>
                <version>${rocketmq.version}</version>
            </dependency>
            <!-- 额外的依赖 -->
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-tools</artifactId>
                <version>${rocketmq.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-namesrv</artifactId>
                <version>${rocketmq.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-classic</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-broker</artifactId>
                <version>${rocketmq.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-classic</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${redis.clients.version}</version>
            </dependency>

            <dependency>
                <groupId>io.seata</groupId>
                <artifactId>seata-spring-boot-starter</artifactId>
                <version>${seata.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>${curator.version}</version>
            </dependency>

            <!-- MinIO -->
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>

            <!-- 阿里云 OSS -->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun.version}</version>
            </dependency>

            <!-- AWS S3 兼容oss、minio -->
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.lookfirst</groupId>
                <artifactId>sardine</artifactId>
                <version>${webdav.version}</version>
            </dependency>

            <dependency>
                <groupId>net.sf.sevenzipjbinding</groupId>
                <artifactId>sevenzipjbinding</artifactId>
                <version>16.02-2.01</version>
            </dependency>

            <dependency>
                <groupId>net.sf.sevenzipjbinding</groupId>
                <artifactId>sevenzipjbinding-all-platforms</artifactId>
                <version>16.02-2.01</version>
            </dependency>

            <!-- pagehelper 分页插件包-->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>5.1.8</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-classic</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-core</artifactId>
                    </exclusion>
                </exclusions>
                <version>1.2.12</version>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>3.3.3</version>
            </dependency>

            <!-- junit -->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.12</version>
            </dependency>

            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>2.0.9</version>
            </dependency>

            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>2.0.9</version>
            </dependency>

            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>2.0.9</version>
            </dependency>

            <dependency>
                <groupId>org.jacoco</groupId>
                <artifactId>org.jacoco.agent</artifactId>
                <version>${jacoco.version}</version>
                <classifier>runtime</classifier>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M1</version>
                <type>maven-plugin</type>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.linker</groupId>
                <artifactId>log-metric-starter</artifactId>
                <version>2.0.2-SNAPSHOT</version>
            </dependency>

            <!-- 无锁队列做异步 -->
            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>${disruptor.version}</version>
            </dependency>

            <dependency>
                <groupId>com.gbase.jdbc</groupId>
                <artifactId>gbase-connector-java</artifactId>
                <version>3.5.1_3X1_3</version>
            </dependency>

            <!--OHC相关-->
            <dependency>
                <groupId>org.caffinitas.ohc</groupId>
                <artifactId>ohc-core</artifactId>
                <version>${ohc.version}</version>
            </dependency>

            <!--OHC 存储的是二进制数组，所以需要实现OHC序列化接口，将缓存数据与二进制数组之间序列化和反序列化-->
            <!--这里使用的是protostuff，当然也可以使用kryo、Hession等，通过压测验证选择适合的-->
            <dependency>
                <groupId>io.protostuff</groupId>
                <artifactId>protostuff-core</artifactId>
                <version>${protostuff.version}</version>
            </dependency>
            <dependency>
                <groupId>io.protostuff</groupId>
                <artifactId>protostuff-runtime</artifactId>
                <version>${protostuff.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
