package com.vos.kernel.business.service.strategy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vos.kernel.business.entity.DynamicParamTemplate;
import com.vos.kernel.business.enums.DynamicParamTypeEnum;
import com.vos.kernel.business.factory.DynamicParamFactory;
import com.vos.kernel.business.params.dynamic.AiAbilityParentParams;
import com.vos.kernel.business.service.common.DynamicParamCommonService;
import com.vos.kernel.common.dubbo.DubboDataContext;
import com.vos.kernel.common.utils.SnowFlakeUtil;
import com.vos.kernel.core.api.domain.TaskTypeEntity;
import com.vos.kernel.core.api.dto.dynamic.ApiExecuteParentParamsDTO;
import com.vos.kernel.core.api.rpc.IAiExecutionRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.dynamictp.core.executor.DtpExecutor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description: 该类的定义说明、使用范围
 * @Author: whz
 * @Date: Created in 2023/10/26 17:34
 * @Version:产品版本号
 */
@Service
@Slf4j
public class OtherHandleHandleServiceImpl implements DynamicParamHandleService {

    @Resource
    private DynamicParamCommonService dynamicParamCommonService;

    @Resource
    DtpExecutor otherExecutor;

    @DubboReference
    IAiExecutionRpcService aiExecutionRpcService;

    /**
     * 还没定义回调地址
     */
    @Value("${ability.file_task_upload.sendToV3AiAsyncUrl}")
    private String kernelSendToOtherAiAsync;
    @Override
    public Object dynamicParamHandle(JSONObject param, DynamicParamTemplate dynamicParamTemplate) {
        AiAbilityParentParams params = JSON.parseObject(param.toJSONString(), AiAbilityParentParams.class);
        Long appIdHolder = DubboDataContext.getAppIdHolder();
        String operator = DubboDataContext.getAuthIdHolder();
        String actionId = params.getActionId();

        //校验用户权益
        TaskTypeEntity userOtherAbility = dynamicParamCommonService.getTaskTypeEntity(operator, actionId, DynamicParamTypeEnum.OTHER.getType());

        //是否同步执行
        String apiCallBackUrl = params.getCallBackUrl();
        Boolean isSync = dynamicParamCommonService.isSync(apiCallBackUrl);
        if (isSync) {
            params.setCallBackUrl(kernelSendToOtherAiAsync);
        }

        String apiAppSourceId = String.valueOf(SnowFlakeUtil.getId());
        ApiExecuteParentParamsDTO apiExecuteParentParamsDTO = new ApiExecuteParentParamsDTO();
        apiExecuteParentParamsDTO.setAppId(appIdHolder);
        apiExecuteParentParamsDTO.setOperator(operator);
        apiExecuteParentParamsDTO.setApiAppSourceId(apiAppSourceId);
        apiExecuteParentParamsDTO.setImageUrl(params.getImageUrl());
        apiExecuteParentParamsDTO.setAbilityId(userOtherAbility .getTaskTypeCode());
        apiExecuteParentParamsDTO.setCallBackUrl(params.getCallBackUrl());
        apiExecuteParentParamsDTO.setTaskOrder(params.getTaskOrder());
        //设置额外参数
        apiExecuteParentParamsDTO.setOmModel(params.getOmModel());
        aiExecutionRpcService.dynamicApiExecute(apiExecuteParentParamsDTO, dynamicParamTemplate.getAbilityTemplate(), DynamicParamTypeEnum.OTHER.getType());

        return apiAppSourceId;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        DynamicParamFactory.strategyMap.put("other", this);
    }
}
