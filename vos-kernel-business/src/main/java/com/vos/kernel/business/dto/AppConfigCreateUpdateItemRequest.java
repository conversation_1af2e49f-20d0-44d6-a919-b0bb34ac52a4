package com.vos.kernel.business.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024年07月25日
 * @version: 1.0
 * @description: TODO
 */
@Data
public class AppConfigCreateUpdateItemRequest implements Serializable {

    @NotBlank(message = "configKey不可为空")
    private String configKey;


    @NotBlank(message = "configValue不可为空")
    private String configValue;
}
