package com.vos.kernel.business.params;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/18
 * @description: com.vos.kernel.business.params
 */
@Data
@ApiModel(description = "ai任务调度时间")
public class JobRuntimeParams implements Serializable {

    @ApiModelProperty(value = "开始时间")
    private String beginTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;
}
