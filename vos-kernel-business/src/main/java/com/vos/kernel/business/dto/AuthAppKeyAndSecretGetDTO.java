package com.vos.kernel.business.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年03月20日
 * @version: 1.0
 * @description: TODO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AuthAppKeyAndSecretGetDTO implements Serializable {

    /**
     * 租户信息
     */
    private List<String> tenantIdList;

    /**
     * 平台信息
     */
    private List<String> platformCodeList;


}
