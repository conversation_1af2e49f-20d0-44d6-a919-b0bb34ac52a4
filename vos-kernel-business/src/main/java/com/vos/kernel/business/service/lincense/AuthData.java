package com.vos.kernel.business.service.lincense;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.linker.hub.api.enums.ProductEnum;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: wuyongjun
 * @Description: 授权信息
 * @Date: Created in 2024/3/13
 */

@Data
@ToString
public class AuthData {
    /** 设备码（多个以逗号分隔） */
    private List<String> deviceIdentification;
    
    /** 授权到期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    private LocalDateTime effectiveDate;
    
    /** 授权开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    private LocalDateTime startTime;
    
    /** 是否永久授权，0:否，1:是 */
    private Integer perpetualLicense;
    
    /** 授权方式，0:离线，1:在线 */
    private Integer licenseType;
    
    /** 合同编号 */
    private String contractNumber;
    
    /** 授权id（需存储，心跳和上报时候会用到） */
    private Long empowerId;
    
    /** @see ProductEnum 产品唯一code */
    private Integer productCode;
    
    /** 需要上报的商品code（多个以逗号分隔） */
    private List<String> goodsCode;
    
    /** 心跳频率，单位：小时 */
    private Integer intervalTime;
    
    /** 有效截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    private LocalDateTime validTime;
    
    /** Os 平台 gpu数量限制 */
    private Integer gpuLimit;
    
    /** 训练卡数量限制 */
    private Integer trainCardLimit;
    
    /** 训练并发数限制 */
    private Integer trainConcurrentLimit;
    
    /** 是否支持模型 omDet */
    private Boolean omDet;
    
    /** 是否支持模型 omChat */
    private Boolean omChat;
    
    /** 产品名称 */
    private String productName;
    
    /** 基础产品名称 */
    private String prodName;
    
    /** 业务冗余数据 */
    private JSONObject redundanciesData;
}
