package com.vos.kernel.business.dto;

import com.linker.omos.client.domain.response.OdResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024年05月23日
 * @version: 1.0
 * @description: TODO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OdAsyncBack implements Serializable {
    private String callbackUrl;

    private OdResponse data;
}
