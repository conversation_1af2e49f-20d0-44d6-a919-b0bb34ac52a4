package com.vos.kernel.business.service;

import com.alibaba.fastjson.JSONObject;
import com.linker.basic.baseclass.BaseResp;
import com.vos.kernel.business.dto.PlatformModelLabelRequest;
import com.vos.kernel.business.dto.RegisterAbilityUpdateRequest;
import com.vos.kernel.business.dto.SyncPlatformModelRequest;
import com.vos.kernel.common.entity.OmInfoGetDTO;
import com.vos.kernel.common.entity.OmInfoUpdateDTO;
import com.vos.kernel.core.api.domain.TTaskTypeOrgSetting;
import com.vos.kernel.core.api.domain.TaskTypeEntity;
import com.vos.kernel.core.api.vo.AppManageReqVO;
import com.vos.kernel.core.api.vo.AppManageSwitchDTO;
import com.vos.kernel.core.api.vo.CalculateMsgListReq;
import com.vos.kernel.core.api.vo.OnlineInstallReqVO;
import com.vos.task.automated.api.model.dto.AbilityAddWithOutOmDTO;
import com.vos.task.automated.api.model.dto.OmInfoDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/17
 * @description: 应用管理服务类
 */

public interface AppManageManagerService {

    BaseResp uploadOMToWebDav(MultipartFile omFile);

    BaseResp uploadOMToAutomat(MultipartFile omFile);

    BaseResp uploadOMToOnline(OnlineInstallReqVO onlineInstallReqVO);

    String installCallback(JSONObject jsonObject);

    BaseResp onlineInstallCallback(JSONObject jsonObject);

    String enableCallback(JSONObject jsonObject);

    BaseResp updateCallback(JSONObject jsonObject);

    BaseResp offlineActivationCallback(JSONObject jsonObject);

    BaseResp install(AppManageReqVO reqVO);

    /**
     * 获取om包信息
     *
     * @param omInfoGet
     * @return
     */
    OmInfoDTO getOmInfo(OmInfoGetDTO omInfoGet);

    BaseResp installV3();

    BaseResp installV35(AppManageReqVO reqVO);

    BaseResp uninstall(AppManageReqVO reqVO);

    BaseResp update(AppManageReqVO reqVO);

    BaseResp determine(String timestamp);

    BaseResp cancel(String timestamp);

    BaseResp getAbnormalCount(String orgId);

    BaseResp rollback(AppManageReqVO reqVO);

    BaseResp rollbackCallback(JSONObject jsonObject);

    boolean isExistTask(String taskTypeCode);

    BaseResp enable(AppManageReqVO reqVO);

    boolean switchApp(AppManageSwitchDTO switchDTO);

    BaseResp calculateMsgList(CalculateMsgListReq req);

    BaseResp saveCalculateMsg(TTaskTypeOrgSetting req);

    BaseResp syncInstall(AbilityAddWithOutOmDTO abilityAdd);

    BaseResp onlineInstall(AppManageReqVO reqVO);

    BaseResp offlineInstall(AppManageReqVO reqVO);

    BaseResp offlineActivation(AppManageReqVO reqVO);

    BaseResp blockUpdate() throws Exception;

    /**
     * 注册算法信息更新
     *
     * @param request
     * @return
     */
    Boolean syncInstallUpdate(RegisterAbilityUpdateRequest request, TaskTypeEntity userAbility);

    /**
     * 更新om包信息
     *
     * @param omInfo
     * @return
     */
    Boolean omInfoUpdate(OmInfoUpdateDTO omInfo);

    /**
     * 同步平台模型
     *
     * @param syncPlatformModelRequest
     * @return
     */
    Boolean syncPlatformModel(SyncPlatformModelRequest syncPlatformModelRequest);

    /**
     * 设置平台模型标签
     *
     * @param platformModelLabelRequest
     * @return
     */
    Boolean setPlatformModelLabel(PlatformModelLabelRequest platformModelLabelRequest);


    /**
     * 获取平台模型标签
     *
     * @param taskTypeCode
     * @return
     */
    String getPlatformModelLabel(String taskTypeCode);

    /**
     * 获取od模型标签
     *
     * @param taskTypeCode
     * @return
     */
    List<String> getOdModelLabel(String taskTypeCode);
}
