package com.vos.kernel.business.task;

import com.vos.kernel.business.service.TaskAbilityTrendStatService;
import com.vos.task.manage.api.model.entity.TaskAbility;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.CheckReturnValue;
import javax.annotation.Resource;

/**
 * @Description: 该类的定义说明、使用范围
 * @Author: whz
 * @Date: Created in 2023/9/12 18:31
 * @Version:产品版本号
 */
@Component
@Slf4j
public class DelTaskAppTrendTask {

    @Resource
    private TaskAbilityTrendStatService taskAbilityTrendStatService;

    @Value("${task.app.trend.task:7}")
    private Integer delDay;
    @XxlJob(value = "delTaskAppTrendTask")
    public ReturnT<String> delTaskAppTrendTask() {
        log.info(">>>>>删除应用趋势统计开始执行，删除天数:{}<<<<<", delDay);
        try {
            Integer count = taskAbilityTrendStatService.delTaskAppTrendTask(delDay);
            if (count > 0){
                log.info("本次删除数量:{}", count);
            } else {
                log.info("没有可删除数据");
            }
        }catch (Exception e) {
            log.error("删除应用趋势失败");
        }
        return ReturnT.SUCCESS;
    }


}
