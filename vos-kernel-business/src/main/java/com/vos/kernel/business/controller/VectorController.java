package com.vos.kernel.business.controller;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.fastjson.JSONObject;
import com.linker.basic.aop.FunctionPoint;
import com.linker.basic.baseclass.BaseResp;
import com.linker.basic.enums.InterfaceType;
import com.vos.kernel.business.service.IApiService;
import com.vos.kernel.common.constant.RedisKey;
import com.vos.kernel.core.api.dto.VectorByDataDTO;
import com.vos.kernel.core.api.dto.VectorSearchDTO;
import com.vos.kernel.core.api.dto.VectorSubmitDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.Duration;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/13
 * @description: com.vos.kernel.business.controller
 */
@Slf4j
@RestController
@RequestMapping("/vector")
@Api(value = "AI配置相关")
public class VectorController {

    @Resource
    private IApiService apiService;
    @Resource
    StringRedisTemplate stringRedisTemplate;

    @SentinelResource(value = "vector")
    @FunctionPoint(interfaceType = InterfaceType.API)
    @ApiOperation(value = "入库向量")
    @PostMapping("/submit")
    public BaseResp vectorSubmit(@RequestBody VectorSubmitDTO vectorSubmitDTO) {
        return new BaseResp(apiService.vectorSubmit(vectorSubmitDTO));
    }

    @FunctionPoint(interfaceType = InterfaceType.API)
    @ApiOperation(value = "搜索")
    @PostMapping("/search")
    public BaseResp vectorSearch(@RequestBody @Valid VectorSearchDTO vectorSearchDTO) {
        return new BaseResp(apiService.vectorSearch(vectorSearchDTO));
    }


    @FunctionPoint(interfaceType = InterfaceType.PUBLIC)
    @ApiOperation(value = "搜索")
    @PostMapping("/callback")
    public BaseResp vectorSearch(@RequestBody @Valid JSONObject object) {
        log.debug("fusion回调：{}", JSONObject.toJSONString(object));
        apiService.vectorBack(object);
        return new BaseResp();
    }

    @SentinelResource(value = "dataToVector")
    @FunctionPoint(interfaceType = InterfaceType.API)
    @ApiOperation(value = "转换为向量")
    @PostMapping("/dataToVector")
    public BaseResp dataToVector(@RequestBody @Valid VectorByDataDTO vectorByDataDTO) {
        return new BaseResp(apiService.dataToVector(vectorByDataDTO));
    }

    @FunctionPoint(interfaceType = InterfaceType.PUBLIC)
    @PostMapping("/search/callBackUrlPresent")
    public BaseResp callBackUrlPresent(@RequestBody @Valid JSONObject object) {
        String apiAppSourceId = object.getString("apiAppSourceId");
        log.debug("搜索回调:{}", apiAppSourceId);
        Duration duration = Duration.ofMinutes(2);
        stringRedisTemplate.opsForValue().set(RedisKey.VECTOR_SEARCH_PRESENT + apiAppSourceId, JSONObject.toJSONString(object), duration);
        return new BaseResp();
    }
}