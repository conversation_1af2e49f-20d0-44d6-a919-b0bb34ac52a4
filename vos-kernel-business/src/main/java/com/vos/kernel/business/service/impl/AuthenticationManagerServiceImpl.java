package com.vos.kernel.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileWriter;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.linker.basic.baseclass.BaseResp;
import com.linker.basic.exception.BusinessException;
import com.linker.core.utils.cnsign.SM2Util;
import com.linker.core.utils.cnsign.SM4Util;
import com.vos.kernel.business.config.OmHubProperties;
import com.vos.kernel.business.dto.OmHubLicenceGetDTO;
import com.vos.kernel.business.exception.AuthenticationException;
import com.vos.kernel.business.service.AuthenticationManagerService;
import com.vos.kernel.business.service.IOmhubAsyncService;
import com.vos.kernel.business.service.lincense.AuthData;
import com.vos.kernel.business.service.lincense.LicenseData;
import com.vos.kernel.business.utils.AuthorizationUtils;
import com.vos.kernel.common.redis.contant.RedisConstant;
import com.vos.kernel.common.utils.EnterpriseCodeUtils;
import com.vos.kernel.common.utils.MD5Util;
import com.vos.kernel.common.utils.RsaUtils;
import com.vos.kernel.core.api.domain.TEquipmentRule;
import com.vos.kernel.core.api.domain.TUserAuthentication;
import com.vos.kernel.core.api.rpc.TEquipmentRuleRpcService;
import com.vos.kernel.core.api.rpc.TUserAuthenticationRpcService;
import com.vos.kernel.core.api.vo.AuthenticationVO;
import com.vos.kernel.core.api.vo.TUserAuthenticationVO;
import com.vos.kernel.core.api.vo.UploadFileVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/17
 * @description: 授权实现类
 */
@Service
@Slf4j
public class AuthenticationManagerServiceImpl implements AuthenticationManagerService {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    OmHubProperties omHubProperties;

    @Resource
    StringRedisTemplate stringRedisTemplate;

    @Resource
    IOmhubAsyncService omhubAsyncService;

    //授权加密文件后缀
    public static final String fileSuffix = ".lic";

    public static final String PRIVATE_KEY = "privateKey";

    public static final String ENCODE_DATA = "encodedData";

    public static final String CHARSET = "UTF-8";

    @Value("${license.bashPath}")
    private String licenseBathPath;
    @Value("${huizhi.version}")
    private String version;

    @DubboReference
    private TEquipmentRuleRpcService tEquipmentRuleRpcService;

    @DubboReference
    private TUserAuthenticationRpcService tUserAuthenticationRpcService;

    @Value("${spring.ras.prikey}")
    String prikey;

    @Value("${oa.authorizeUrl}")
    private String authorizeUrl;

    //appid
    @Value("${spring.appid}")
    String appid;

    //appid
    @Value("${spring.appSecret}")
    String appSecret;


    /**
     * 上传授权文件
     *
     * @param uploadFileVo
     * @return
     */
    @Override
    public BaseResp uploadLicenseFile(UploadFileVo uploadFileVo) {

        File fileFold = new File(licenseBathPath);
        if (!fileFold.exists()) {
            fileFold.mkdirs();
        }
        String fileName = uploadFileVo.getFile().getOriginalFilename();
        if (!fileName.toLowerCase().endsWith(fileSuffix)) {
            log.info("上传文件后缀不符合规定");
            throw new AuthenticationException("500", "license文件不合法,激活失败");
        }
        File[] files = fileFold.listFiles();
        BufferedReader br = null;
        String line;
        StringBuffer stringBuffer = new StringBuffer();
        String privateKey = null;
        String encodedData = null;

        //将上传的文件写入到指定目录下
        try {
            InputStream inputStream = uploadFileVo.getFile().getInputStream();
            br = new BufferedReader(new InputStreamReader(
                    inputStream, CHARSET));
            while ((line = br.readLine()) != null) {
                log.info(line);
                stringBuffer.append(line).append("\n");
                 /* if (line.contains(PRIVATE_KEY)) {
                    String[] lineSplits = line.split("=");
                    privateKey = lineSplits[1];
                } else*/
                if (line.contains(ENCODE_DATA)) {
                    String[] lineSplits = line.split("=");
                    encodedData = lineSplits[1];
                }
            }
            br.close();
        } catch (Exception e) {
            log.error("IO操作异常", e);
            throw new AuthenticationException("500", "激活失败");
        }

        //获取私钥方式修改
        privateKey = prikey;

        if (privateKey == null || encodedData == null) {
            log.info("上传的license授权文件找不到加密信息内容");
            throw new AuthenticationException("500", "激活失败");
        }
        log.info("私钥信息：{},密文信息：{}", privateKey, encodedData);
        JSONObject uploadJsonObject = null;
        try {
            String pck8 = RsaUtils.formatPkcs1ToPkcs8(privateKey);
            log.info("Upload 私钥转化的pck8:{}", pck8);
            String decodedData = RsaUtils.privateDecrypt(encodedData, RsaUtils.getPrivateKey(pck8));
            log.info("Upload DecodedData:{}", decodedData);
            uploadJsonObject = JSONObject.parseObject(decodedData);
        } catch (Exception e) {
            log.error("上传的license授权信息解密失败", e);
            throw new AuthenticationException("500", "激活失败");
        }

        if (uploadJsonObject == null) {
            log.info("上传的license授权信息解密失败，解析对象为空");
            throw new AuthenticationException("500", "激活失败");
        }
        //校验授权文件相关信息是否为空
        Boolean checkResult = checkLicense(uploadJsonObject, null);
        if (!checkResult) {
            throw new AuthenticationException("500", "校验失败");
        }

        // 校验比对设备码，设备码获取规则从 zhang hao 提供的加密方法获取
        String deviceCode = getRegistrationCode();

        //校验设备码
        BaseResp checkDeviceCodeResult = checkDeviceCode(uploadJsonObject, deviceCode);
        if (0 != Integer.valueOf(checkDeviceCodeResult.getCode())) {
            throw new AuthenticationException("500", "设备码不一致，激活失败");
        }

        //若原有授权文件夹存在授权文件
        if (files.length > 0) {
            JSONObject oldJSONObect = getOldDecryptData(files);
            //校验上传授权文件和已有授权文件信息
            Boolean checkObjectResult = checkLicense(uploadJsonObject, oldJSONObect);
            if (!checkObjectResult) {
                throw new AuthenticationException("500", "校验失败");
            }
            //若校验都已通过，则删除原有的授权文件
            for (File file : files) {
                file.delete();
                log.info("删除已有授权文件：{}", file.getName());
            }
        }
        FileWriter writer = new FileWriter(licenseBathPath + "/" + fileName);
        writer.append(stringBuffer.toString());

        stringRedisTemplate.opsForValue().set(RedisConstant.LICENSE_KEY, encodedData, 30, TimeUnit.MINUTES);

        Date effectiveDate = uploadJsonObject.getDate("effectiveDate");
        TUserAuthenticationVO tUserAuthenticationVO = new TUserAuthenticationVO();
        tUserAuthenticationVO.setHashConsumeAll("0");
        tUserAuthenticationVO.setTaskTypeCodes("");
        tUserAuthenticationVO.setType("1");
        tUserAuthenticationVO.setEffectiveDate(effectiveDate);
        tUserAuthenticationVO.setVideoLimit(0L);
        tUserAuthenticationVO.setLocalLimit(0L);
        TUserAuthentication tUserAuthentication = tUserAuthenticationRpcService.getAdmin();
        if (ObjectUtil.isNotEmpty(tUserAuthentication)) {
            Map hashMap = new HashMap();
            // 设备码获取
            hashMap.put("effectiveDate", effectiveDate);
            hashMap.put("appKey", tUserAuthentication.getAppKey());
            hashMap.put("appSecret", tUserAuthentication.getAppSecret());
            hashMap.put("authenticationId", tUserAuthentication.getAuthenticationId());
            return new BaseResp(hashMap);
        }
        return new BaseResp(tUserAuthenticationRpcService.addUser(tUserAuthenticationVO, uploadJsonObject, deviceCode));
    }

    @Override
    public Boolean cmsUploadLicenseFile(UploadFileVo uploadFileVo) {
        String machineCode = AuthorizationUtils.getMachineCode(null);
        String ENCRYPT_DATA = "encodedData=";
        String authEncryptInfo = "";
        try {
            byte[] bytes = uploadFileVo.getFile().getBytes();
            authEncryptInfo = new String(bytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("离线授权异常，异常信息：", e);
            throw new BusinessException("离线授权文件上传异常");
        }
        if (StrUtil.isBlank(authEncryptInfo)) {
            throw new BusinessException("离线授权文件内容为空");
        }
        log.info("离线授权文件内容：{}", authEncryptInfo);
        if (StrUtil.isNotBlank(authEncryptInfo) && authEncryptInfo.length() >= ENCRYPT_DATA.length()) {
            authEncryptInfo = authEncryptInfo.substring(ENCRYPT_DATA.length()).replaceAll("\n", "");
        }
        String decryptData = SM4Util.decryptDataCBC(authEncryptInfo, omHubProperties.getEvaluationSecretKey(), omHubProperties.getEvaluationIv());
        if (StrUtil.isBlank(decryptData)) {
            throw new BusinessException("license CBC 解析失败");
        }
        String decrypt = SM2Util.decrypt(decryptData, omHubProperties.getEvaluationPriKeyHexString());
        if (StrUtil.isBlank(decrypt)) {
            throw new BusinessException("license SM2 解析失败");
        }
        AuthData authData = JSON.parseObject(decrypt, AuthData.class);
        if (authData == null) {
            throw new BusinessException("license 解析失败");
        }
        Integer productCode = authData.getProductCode();
        if (productCode == null || !productCode.equals(omHubProperties.getProductCode())) {
            throw new BusinessException("授权平台不匹配");
        }
        Integer licenseType = authData.getLicenseType();
        if (licenseType == null || !licenseType.equals(0)) {
            throw new BusinessException("授权方式不匹配");
        }
        List<String> deviceIdentification = authData.getDeviceIdentification();
        if (CollectionUtil.isEmpty(deviceIdentification) || !deviceIdentification.contains(machineCode)) {
            throw new BusinessException("授权设备不匹配");
        }
        Integer a = 1;
        if (a.equals(authData.getPerpetualLicense())) {
            authData.setEffectiveDate(LocalDateTime.of(9999, 12, 31, 23, 59, 59));
            authData.setStartTime(LocalDateTimeUtil.offset(LocalDateTime.now(), -10, ChronoUnit.MINUTES));
        }
        if (authData.getEffectiveDate() == null) {
            throw new BusinessException("授权有效期解析失败");
        } else {
            LocalDateTime fixEffectiveDate = authData.getEffectiveDate().withSecond(59);
            authData.setEffectiveDate(fixEffectiveDate);// 有效期秒修正
        }
        if (authData.getStartTime() == null) {
            throw new BusinessException("授权有效期解析失败");
        } else {
            LocalDateTime fixStartTime = authData.getStartTime().withSecond(0);
            authData.setStartTime(fixStartTime);// 开始时间秒修正
        }
        if (authData.getStartTime().isAfter(authData.getEffectiveDate())) {
            throw new BusinessException("授权有效期异常，开始时间不能大于结束时间");
        }
        String jsonString = JSON.toJSONString(authData);
        OmHubLicenceGetDTO omHubLicence = JSON.parseObject(jsonString, OmHubLicenceGetDTO.class);
        omhubAsyncService.setLicenceInfo(omHubLicence);
        //保存到本地目录
        String fileName = machineCode + ".license";
        String path = licenseBathPath + "/" + fileName;
        FileUtil.writeUtf8String(jsonString, path);
        return true;
    }

    /**
     * 在线激活
     *
     * @param authenticationVO
     * @return
     */
    @Override
    public BaseResp onlineActivation(AuthenticationVO authenticationVO) {

        if (StringUtils.isEmpty(authenticationVO.getUuid())) {
            throw new AuthenticationException("500", "uuid不可为空");
        }

        if (StringUtils.isEmpty(authenticationVO.getAuthorizationCode())) {
            throw new AuthenticationException("500", "授权码不可为空");
        }

        File fileFold = new File(licenseBathPath);
        if (!fileFold.exists()) {
            fileFold.mkdirs();
        }

        File[] files = fileFold.listFiles();
        BufferedReader br = null;
        String line;
        StringBuffer stringBuffer = new StringBuffer();
        String privateKey = null;
        String oldlicense = null;
        if (files.length > 0) {
            oldlicense = getOldDecryptData(files).toString();
        }

        Map<String, Object> map = new HashMap<>();
        map.put("uuid", authenticationVO.getUuid());
        map.put("authorizationCode", authenticationVO.getAuthorizationCode());
        map.put("activateAccount", authenticationVO.getActivateAccount());
        map.put("oldlicense", oldlicense);
        String keyTimeStamp = String.valueOf(System.currentTimeMillis());
        String appkey = MD5Util.string2MD5(appid + appSecret + keyTimeStamp);

        HttpHeaders headers = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        headers.setContentType(type);
        headers.set("appkey", appkey);
        headers.set("keyTimeStamp", keyTimeStamp);
        String params = JSONObject.toJSONString(map);
        HttpEntity<String> formEntity = new HttpEntity<String>(params, headers);
        log.info("request  请求接口===>{}  {}  {}", authorizeUrl, params, headers);
        String body = null;
        try {
            body = restTemplate.postForEntity(authorizeUrl, formEntity, String.class).getBody();
        } catch (RestClientException e) {
            log.error(e.getMessage());
            throw new BusinessException("license生成异常，激活失败");
        }
        JSONObject jsonObject = JSONObject.parseObject(body);

        if (!"200".equals(jsonObject.get("code").toString())) {
            throw new AuthenticationException("500", jsonObject.get("msg").toString());
        }
        JSONObject data = JSONObject.parseObject(jsonObject.get("data").toString());

        String encodedData = data.getString("license");

        //获取私钥方式修改
        privateKey = prikey;

        if (privateKey == null || encodedData == null) {
            throw new AuthenticationException("500", "激活失败");
        }
        stringBuffer.append("encodedData=" + encodedData).append("\n");
        log.info("私钥信息：{},密文信息：{}", privateKey, encodedData);
        JSONObject uploadJsonObject = null;
        try {
            String pck8 = RsaUtils.formatPkcs1ToPkcs8(privateKey);
            log.info("Upload 私钥转化的pck8:{}", pck8);
            String decodedData = RsaUtils.privateDecrypt(encodedData, RsaUtils.getPrivateKey(pck8));
            log.info("Upload DecodedData:{}", decodedData);
            uploadJsonObject = JSONObject.parseObject(decodedData);
        } catch (Exception e) {
            log.error("上传的license授权信息解密失败", e);
            throw new AuthenticationException("500", "激活失败");
        }

        if (uploadJsonObject == null) {
            log.info("上传的license授权信息解密失败，解析对象为空");
            throw new AuthenticationException("500", "激活失败");
        }
        //校验授权文件相关信息是否为空
        Boolean checkResult = checkLicense(uploadJsonObject, null);
        if (!checkResult) {
            throw new AuthenticationException("500", "校验失败");
        }

        // 校验比对设备码，设备码获取规则从 zhang hao 提供的加密方法获取
        String deviceCode = getRegistrationCode();

        //校验设备码
        BaseResp checkDeviceCodeResult = checkDeviceCode(uploadJsonObject, deviceCode);
        if (0 != Integer.valueOf(checkDeviceCodeResult.getCode())) {
            throw new AuthenticationException("500", "设备码不一致，激活失败");
        }

        //若原有授权文件夹存在授权文件
        if (files.length > 0) {
            JSONObject oldJSONObect = getOldDecryptData(files);
            //校验上传授权文件和已有授权文件信息
            Boolean checkObjectResult = checkLicense(uploadJsonObject, oldJSONObect);
            if (!checkObjectResult) {
                throw new AuthenticationException("500", "校验失败");
            }
            //若校验都已通过，则删除原有的授权文件
            for (File file : files) {
                file.delete();
                log.info("删除已有授权文件：{}", file.getName());
            }
        }
        FileWriter writer = new FileWriter(licenseBathPath + "/" + "视觉认知服务平台.lic");
        writer.append(stringBuffer.toString());

        stringRedisTemplate.opsForValue().set(RedisConstant.LICENSE_KEY, encodedData, 30, TimeUnit.MINUTES);

        Date effectiveDate = uploadJsonObject.getDate("effectiveDate");
        TUserAuthenticationVO tUserAuthenticationVO = new TUserAuthenticationVO();
        tUserAuthenticationVO.setHashConsumeAll("0");
        tUserAuthenticationVO.setTaskTypeCodes("");
        tUserAuthenticationVO.setType("1");
        tUserAuthenticationVO.setEffectiveDate(effectiveDate);
        tUserAuthenticationVO.setVideoLimit(0L);
        tUserAuthenticationVO.setLocalLimit(0L);
        TUserAuthentication tUserAuthentication = tUserAuthenticationRpcService.getAdmin();
        if (ObjectUtil.isNotEmpty(tUserAuthentication)) {
            Map hashMap = new HashMap();
            // 设备码获取
            hashMap.put("effectiveDate", effectiveDate);
            hashMap.put("appKey", tUserAuthentication.getAppKey());
            hashMap.put("appSecret", tUserAuthentication.getAppSecret());
            hashMap.put("authenticationId", tUserAuthentication.getAuthenticationId());
            return new BaseResp(hashMap);
        }
        return new BaseResp(tUserAuthenticationRpcService.addUser(tUserAuthenticationVO, uploadJsonObject, deviceCode));
    }

    @Override
    public String getEnterpriseCode(String appKey) {

        if (ObjectUtil.isEmpty(appKey)) {
            throw new AuthenticationException("500", "appKey不可为空");
        }

        TUserAuthentication tUserAuthentication = tUserAuthenticationRpcService.getUserInfo(appKey);

        if (ObjectUtil.isNull(tUserAuthentication)) {
            throw new AuthenticationException("500", "appKey不存在");
        }
        String authCode = getRegistrationCode();
        log.info("appKey：{}", appKey);
        log.info("机器码：{}", authCode);
        String enterpriseCode = EnterpriseCodeUtils.getEnterpriseCode(appKey, authCode);
        log.info("企业标识：{}", enterpriseCode);
        return enterpriseCode;
    }


    /**
     * 校验上传授权文件和已有授权文件
     *
     * @param uploadJsonObject
     * @param oldJSONObect
     * @return
     */
    public Boolean checkLicense(JSONObject uploadJsonObject, JSONObject oldJSONObect) {
        String uploadDeviceIdentification = uploadJsonObject.getString("deviceIdentification");
        Integer uploadDeviceLimit = uploadJsonObject.getInteger("deviceLimit");
        Integer uploadOrganLimit = uploadJsonObject.getInteger("organLimit");
        Integer uploadAppLimit = uploadJsonObject.getInteger("appLimit");
        //新增授权校验信息
        Integer uploadServerLimit = uploadJsonObject.getInteger("serverLimit");
        Integer uploadGpuLimit = uploadJsonObject.getInteger("gpuLimit");
        Integer uploadComputationalPowerLimit = uploadJsonObject.getInteger("computationalPowerLimit");
        String effectiveDate = uploadJsonObject.getString("effectiveDate");
        //VOS授权管理新增授权校验信息
        Integer applicationLimit = uploadJsonObject.getInteger("applicationLimit");
        Integer taskChannelUpperLimit = uploadJsonObject.getInteger("taskChannelUpperLimit");


        log.info("uploadDeviceIdentification:{},uploadDeviceLimit:{},uploadOrganLimit:{}," +
                        "uploadAppLimit:{},uploadServerLimit:{},uploadGpuLimit:{},uploadComputationalPowerLimit:{},effectiveDate:{}",
                uploadDeviceIdentification, uploadDeviceLimit, uploadOrganLimit, uploadAppLimit, uploadServerLimit, uploadGpuLimit, uploadComputationalPowerLimit, effectiveDate);
        if (uploadDeviceIdentification == null || uploadDeviceLimit == null
                || uploadOrganLimit == null || uploadAppLimit == null /*|| uploadServerLimit == null || uploadGpuLimit == null*/
                || uploadComputationalPowerLimit == null || effectiveDate == null) {
            throw new AuthenticationException("500", "授权信息缺失,激活失败");

        }
        LocalDate uploadExpirLocalDate = LocalDate.parse(effectiveDate);

        if (LocalDate.now().isAfter(uploadExpirLocalDate)) {
            log.info("客户名称:{} 上传授权文件已过期，无法上传", uploadJsonObject.getString("customerName"));
            throw new AuthenticationException("500", "授权文件已过期,激活失败");
        }


        if (oldJSONObect != null) {
            String oldDeviceIdentification = oldJSONObect.getString("deviceIdentification");
            Integer oldDeviceLimit = oldJSONObect.getInteger("deviceLimit");
            Integer oldOrganLimit = oldJSONObect.getInteger("organLimit");
            Integer oldAppLimit = oldJSONObect.getInteger("appLimit");
            //新增授权信息
            Integer oldServerLimit = oldJSONObect.getInteger("serverLimit");
            Integer oldGpuLimit = oldJSONObect.getInteger("gpuLimit");
            Integer oldComputationalPowerLimit = oldJSONObect.getInteger("computationalPowerLimit");
            String oldEffectiveDate = oldJSONObect.getString("effectiveDate");
            //VOS授权管理新增授权校验信息
            Integer oldApplicationLimit = oldJSONObect.getInteger("applicationLimit");
            Integer oldTaskChannelUpperLimit = oldJSONObect.getInteger("taskChannelUpperLimit");


            log.info("oldDeviceIdentification:{},oldDeviceLimit:{},oldOrganLimit:{}," +
                            "oldAppLimit:{},oldServerLimit:{},oldGpuLimit:{},oldComputationalPowerLimit:{},oldEffectiveDate:{}",
                    oldDeviceIdentification, oldDeviceLimit, oldOrganLimit, oldAppLimit, oldServerLimit, oldGpuLimit, oldComputationalPowerLimit, oldEffectiveDate);

//            if (!uploadDeviceIdentification.equals(oldDeviceIdentification)) {
//                return CommonResult.uploadValidateFailed(uploadJsonObject, "无效的license");
//            }
            //校验过期时间
            if (oldEffectiveDate != null) {
                LocalDate expirLocalDate = LocalDate.parse(effectiveDate);
                LocalDate oldExpirLocalDate = LocalDate.parse(oldEffectiveDate);
                if (oldExpirLocalDate.isAfter(expirLocalDate)) {
                    throw new AuthenticationException("500", "新授权权限小于当前已授权权限，未更新授权");
                }
            }
            //校验接入设备上限
            if (oldDeviceLimit != null && uploadDeviceLimit < oldDeviceLimit) {
                throw new AuthenticationException("500", "新授权权限小于当前已授权权限，未更新授权");
            }
            //校验接入机构上限
            if (oldOrganLimit != null && uploadOrganLimit < oldOrganLimit) {
                throw new AuthenticationException("500", "新授权权限小于当前已授权权限，未更新授权");
            }
            //校验接入应用上限
            if (oldAppLimit != null && uploadAppLimit < oldAppLimit) {
                throw new AuthenticationException("500", "新授权权限小于当前已授权权限，未更新授权");
            }
            //校验服务器安装上限
            if (oldServerLimit != null && uploadServerLimit != null) {
                if (uploadServerLimit < oldServerLimit) {
                    throw new AuthenticationException("500", "新授权权限小于当前已授权权限，未更新授权");
                }
            }
            //校验GPU安装上限
            if (oldGpuLimit != null && uploadGpuLimit != null) {
                if (uploadGpuLimit < oldGpuLimit) {
                    throw new AuthenticationException("500", "新授权权限小于当前已授权权限，未更新授权");
                }
            }
            //校验算力上限
            if (oldComputationalPowerLimit != null && uploadComputationalPowerLimit < oldComputationalPowerLimit) {
                throw new AuthenticationException("500", "新授权权限小于当前已授权权限，未更新授权");
            }

            //校验应用上限
            if (oldApplicationLimit != null && applicationLimit != null) {
                if (applicationLimit < oldApplicationLimit) {
                    throw new AuthenticationException("500", "新授权权限小于当前已授权权限，未更新授权");
                }
            }

            //校验任务通道上限
            if (oldTaskChannelUpperLimit != null && taskChannelUpperLimit != null) {
                if (taskChannelUpperLimit < oldTaskChannelUpperLimit) {
                    throw new AuthenticationException("500", "新授权权限小于当前已授权权限，未更新授权");
                }
            }
        }
        return true;
    }

    /**
     * 获取已存在的授权文件的解密信息
     *
     * @param files
     * @return
     */
    public JSONObject getOldDecryptData(File[] files) {
        String privateKey = null;
        JSONObject jsonObject = null;

        String encodedData = stringRedisTemplate.opsForValue().get(RedisConstant.LICENSE_KEY);

        if (ObjectUtil.isEmpty(encodedData)) {
            File licFile = null;
            for (File file : files) {
                if (file.getName().toLowerCase().endsWith(fileSuffix)) {
                    licFile = file;
                }
            }
            if (licFile != null) {

                BufferedReader br = null;
                String line;
                try {
                    br = new BufferedReader(new InputStreamReader(
                            new FileInputStream(licFile), CHARSET));
                    while ((line = br.readLine()) != null) {
                        log.info(line);
                    /*if (line.contains(PRIVATE_KEY)) {
                        String[] lineSplits = line.split("=");
                        privateKey = lineSplits[1];
                      } else */
                        if (line.contains(ENCODE_DATA)) {
                            String[] lineSplits = line.split("=");
                            encodedData = lineSplits[1];
                        }
                    }
                    br.close();
                } catch (IOException e) {
                    log.error("IO操作异常", e);
                    throw new AuthenticationException("500", "IO操作异常");
                }
            }
        }
        //获取私钥方式修改
        privateKey = prikey;

        if (privateKey == null || ObjectUtil.isEmpty(encodedData)) {
            log.info("文件查找失败，license文件找不到加密信息内容，文件夹路径：{}", licenseBathPath);
            throw new AuthenticationException("1002", "license文件找不到加密信息内容");
        }
        stringRedisTemplate.opsForValue().set(RedisConstant.LICENSE_KEY, encodedData, 30, TimeUnit.MINUTES);
        log.info("私钥信息：{},密文信息：{}", privateKey, encodedData);
        try {
            String pck8 = RsaUtils.formatPkcs1ToPkcs8(privateKey);
            log.info("私钥转化的pck8:{}", pck8);
            String decodedData = RsaUtils.privateDecrypt(encodedData, RsaUtils.getPrivateKey(pck8));
            log.info("OLD-DecodedData:{}", decodedData);
            jsonObject = JSONObject.parseObject(decodedData);
        } catch (Exception e) {
            log.error("license解密失败", e);
            throw new AuthenticationException("1003", "license解密失败");
        }
        if (jsonObject == null) {
            throw new AuthenticationException("1003", "获取解密信息失败");
        }
        return jsonObject;
    }

    @Override
    public OmHubLicenceGetDTO getLicenceInfo() {
        OmHubLicenceGetDTO licenceInfo = null;
        try {
            licenceInfo = omhubAsyncService.getLicenceInfo();
        } catch (Exception e) {
            log.error("获取授权信息失败", e);
        }
        if (null != licenceInfo) {
            licenceInfo.setHasLicense(true);
            return licenceInfo;
        } else {
            OmHubLicenceGetDTO omHubLicenceGet = new OmHubLicenceGetDTO();
            omHubLicenceGet.setDeviceIdentification(Collections.singletonList(AuthorizationUtils.getMachineCode(null)));
            omHubLicenceGet.setHasLicense(false);
            return omHubLicenceGet;
        }

    }

    /**
     * 获取授权解密信息
     * 1.上传授权文件至该系统的相对licens文件夹目录下
     * 2.判断文件后缀.lic 若不是则返回失败
     * 3.上传时先判断对应文件夹目录下使用已有文件，已有则进行删除
     *
     * @return
     */
    @Override
    public BaseResp getDecryptData() {
/* String str="{\n" +
                "        \"serverLimit\":9999,\n" +
                "        \"appLimit\":9999,\n" +
                "        \"gpuLimit\":9999,\n" +
                "        \"organLimit\":9999,\n" +
                "        \"computationalPowerLimit\":9999,\n" +
                "        \"remark\":\"备注\",\n" +
                "        \"deviceIdentification\":\"2B065-2A41C-30F8D-23A6F-93623-5CBA4\",\n" +
                "        \"authCode\":\"2B065-2A41C-30F8D-23A6F-93623-5CBA4\",\n" +
                "        \"authorizationSystem\":\"OmVision OS视觉操作系统V2.0.0\",\n" +
                "        \"deviceLimit\":9999,\n" +
                "        \"customerName\":\"测试\",\n" +
                "        \"effectiveDate\":\"2024-05-31\"\n" +
                "    }";

        return new BaseResp(JSONObject.parseObject(str));*/
        if (BooleanUtil.isTrue(omHubProperties.getOpen())) {
            OmHubLicenceGetDTO licenceInfo = omhubAsyncService.getLicenceInfo();
            String jsonString = JSON.toJSONString(licenceInfo);
            log.info("hub 授权信息：{}", jsonString);
            if (StrUtil.isNotBlank(jsonString)) {
                JSONObject jsonObject = JSON.parseObject(jsonString);
                //兼容历史
                jsonObject.put("appLimit", 9999);
                jsonObject.put("organLimit", 9999);
                jsonObject.put("deviceLimit", 9999);
                jsonObject.put("localLimit", 9999);
                jsonObject.put("serverLimit", 9999);
                jsonObject.put("computationalPowerLimit", 9999999);
                return new BaseResp(jsonObject);
            } else {
                return new BaseResp(null);
            }

        } else {
            String privateKey = null;
            JSONObject jsonObject = new JSONObject();
            String encodedData = stringRedisTemplate.opsForValue().get(RedisConstant.LICENSE_KEY);
            String deviceCode = getRegistrationCode();
            if (ObjectUtil.isEmpty(encodedData)) {

                long startTime = System.currentTimeMillis();
                File baseDir = new File(licenseBathPath);
                long endTime = System.currentTimeMillis();
                log.info("获取license文件完成，耗时：{}毫秒", (endTime - startTime));

                // 校验比对设备码，设备码获取规则从 zhang hao 提供的加密方法获取
                jsonObject.put("deviceIdentification", deviceCode);
                jsonObject.put("authorizationSystem", version);

                // 判断目录是否存在
                if (!baseDir.exists() || !baseDir.isDirectory()) {
                    log.info("文件查找失败：{} 不是一个目录！", licenseBathPath);
                    return new BaseResp("1007", "未激活授权", jsonObject);
                }
                File[] files = baseDir.listFiles();
                if (files.length == 0) {
                    log.info("文件查找失败，对应文件夹目录下没有文件，文件夹路径：{}", licenseBathPath);
                    return new BaseResp("1007", "未激活授权", jsonObject);
                }

                File licFile = null;
                for (File file : files) {
                    if (file.getName().toLowerCase().endsWith(fileSuffix)) {
                        licFile = file;
                    }
                }
                if (licFile == null) {
                    log.info("文件查找失败，对应文件夹目录下没有授权加密文件，文件夹路径：{}", licenseBathPath);
                    return new BaseResp("1007", "未激活授权", jsonObject);
                }
                BufferedReader br = null;
                String line;
                try {
                    br = new BufferedReader(new InputStreamReader(
                            new FileInputStream(licFile), CHARSET));
                    while ((line = br.readLine()) != null) {
                        log.info(line);
                        if (line.contains(PRIVATE_KEY)) {
                            String[] lineSplits = line.split("=");
                            privateKey = lineSplits[1];
                        } else if (line.contains(ENCODE_DATA)) {
                            String[] lineSplits = line.split("=");
                            encodedData = lineSplits[1];
                        }
                    }
                    br.close();
                } catch (IOException e) {
                    log.error("IO操作异常", e);
                    return new BaseResp("1007", "未激活授权", jsonObject);
                }
            }
            //获取私钥方式修改
            privateKey = prikey;

            if (privateKey == null || ObjectUtil.isEmpty(encodedData)) {
                log.info("文件查找失败，license文件找不到加密信息内容，文件夹路径：{}", licenseBathPath);
                return new BaseResp("1007", "未激活授权", jsonObject);
            }

            stringRedisTemplate.opsForValue().set(RedisConstant.LICENSE_KEY, encodedData, 30, TimeUnit.MINUTES);

            log.info("私钥信息：{},密文信息：{}", privateKey, encodedData);

            try {
                String pck8 = RsaUtils.formatPkcs1ToPkcs8(privateKey);
                log.info("私钥转化的pck8:{}", pck8);
                long rasStartTime = System.currentTimeMillis();
                String decodedData = RsaUtils.privateDecrypt(encodedData, RsaUtils.getPrivateKey(pck8));
                long rasEndTime = System.currentTimeMillis();
                log.info("license文件解密完成，耗时：{}毫秒", (rasEndTime - rasStartTime));
                log.info("decodedData:{}", decodedData);
                JSONObject decodeObj = JSONObject.parseObject(decodedData);
                if (decodeObj == null) {
                    return new BaseResp("1007", "未激活授权", jsonObject);
                }
                jsonObject.putAll(decodeObj);
            } catch (Exception e) {
                log.error("license解密失败", e);
                return new BaseResp("1007", "未激活授权", jsonObject);
            }

            BaseResp checkDeviceCodeResult = checkDeviceCode(jsonObject, deviceCode);
            if (0 != Integer.valueOf(checkDeviceCodeResult.getCode())) {
                JSONObject json = new JSONObject();
                json.put("deviceIdentification", deviceCode);
                json.put("authorizationSystem", version);
                return new BaseResp("1007", "设备标志发生更改", json);
                //return checkDeviceCodeResult;
            }

            //校验解密出来的授权信息
            //校验授权有效期
            String effectiveDate = jsonObject.getString("effectiveDate");
            if (StringUtils.isEmpty(effectiveDate)) {
                log.info("验证超时时间为空");
                return new BaseResp("1007", "未激活授权", jsonObject);
            }

            LocalDate expirLocalDate = LocalDate.parse(effectiveDate);

            if (LocalDate.now().isAfter(expirLocalDate)) {
                log.info("客户名称:{} 授权信息已过期", jsonObject.getString("customerName"));
                return new BaseResp("1004", "检测到license到期，授权已失效", jsonObject);
            }
            //获取设备码
            String authCode = getRegistrationCode();
            jsonObject.put("authCode", authCode);
            return new BaseResp("0", "已激活授权", jsonObject);
        }

    }


    /**
     * 校验设备码
     *
     * @return
     */
    public BaseResp checkDeviceCode(JSONObject jsonObject, String deviceCode) {
        //密文中的设备编码
        String ciphertextDeviceCode = jsonObject.getString("deviceIdentification");
        log.info("获取的设备码信息：{},密文中的设备码信息：{}", deviceCode, ciphertextDeviceCode);
        if (!ciphertextDeviceCode.equals(deviceCode)) {
            log.info("客户名称:{} 设备标志不一致，密文设备标志：{}，实际设备标志：{}",
                    jsonObject.getString("customerName"), ciphertextDeviceCode, deviceCode);
            jsonObject.put("deviceIdentification", deviceCode);
            return new BaseResp("500", "设备ID未匹配，激活失败", jsonObject);
        }
        return new BaseResp();
    }

    /**
     * 获取设备码
     * 设备码获取规则从 zhang hao 提供的加密方法获取
     *
     * @return
     */
    @Override
    public String getRegistrationCode() {

        Set<String> param = new HashSet<>();
        param.add(version);

        //注册码添加设备规则逻辑 by fangyixiong 2022-09-13
        String equipmentRule = null;

        TEquipmentRule tEquipmentRule = tEquipmentRuleRpcService.findEquipmentRule();
        if (ObjectUtil.isNotEmpty(tEquipmentRule)) {
            equipmentRule = tEquipmentRule.getEquipmentRule();
        }
        return AuthorizationUtils.getRegistrationCode(param, equipmentRule);

    }
}
