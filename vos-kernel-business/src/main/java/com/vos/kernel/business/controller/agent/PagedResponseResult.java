package com.vos.kernel.business.controller.agent;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.linker.core.base.enums.RespCodeEnum;

import lombok.Data;

@Data
public class PagedResponseResult<T> {
    private String code;
    private String message;
    private Pagination pagination;
    private T data;

    @Data
    public static class Pagination {
        private long total;
        private long current;
        private long size;
    }

    public static <T> PagedResponseResult<T> success(T data) {
        return success(data, RespCodeEnum.SUCCESS.getCode(), RespCodeEnum.SUCCESS.getMessage());
    }

    public static <T> PagedResponseResult<T> success(T data, String code, String message) {
        PagedResponseResult<T> result = new PagedResponseResult<>();
        result.setCode(code);
        result.setMessage(message);
        result.setData(data);
        return result;
    }

    public static <T> PagedResponseResult<T> success(T data, IPage<?> page) {
        return success(data, page, RespCodeEnum.SUCCESS.getCode(), RespCodeEnum.SUCCESS.getMessage());
    }

    public static <T> PagedResponseResult<T> success(T data, IPage<?> page, String code, String message) {
        PagedResponseResult<T> result = new PagedResponseResult<>();
        result.setCode(code);
        result.setMessage(message);
        result.setData(data);

        Pagination pagination = new Pagination();
        pagination.setTotal(page.getTotal());
        pagination.setCurrent(page.getCurrent());
        pagination.setSize(page.getSize());
        result.setPagination(pagination);

        return result;
    }

    public static <T> PagedResponseResult<T> error(String code, String message) {
        PagedResponseResult<T> result = new PagedResponseResult<>();
        result.setCode(code);
        result.setMessage(message);
        return result;
    }
}