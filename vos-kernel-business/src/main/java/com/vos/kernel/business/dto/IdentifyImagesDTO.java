package com.vos.kernel.business.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@Validated
public class IdentifyImagesDTO implements Serializable {

//    @NotBlank(message = "第三方回调不可为空")
    @ApiModelProperty(value = "第三方回调地址")
    private String apiCallBackUrl;
    @NotBlank(message = "设备标识不可为空")
    @ApiModelProperty(value = "设备标识")
    private String videoCode;

    @ApiModelProperty(value = "第三方业务参数")
    private String businessParam;
    @ApiModelProperty(value = "是否携带ai全部参数")
    private Boolean aiOriginalBack = false;

    @ApiModelProperty(value = "任务关联应用信息")
    private List<IdentifyImagesSubDTO> subList;

    @ApiModelProperty(value = "图像信息")
    private List<IdentifyImageInfoDTO> fileInfoList;

    @ApiModelProperty(value = "调用请求标识")
    private String appSourceId;

    @NotNull(message = "文件类型不可为空")
    @ApiModelProperty(value = "文件类型,1image,2base64,3视频流")
    private Integer fileType;

    @ApiModelProperty(value = "算法异常回调地址")
    private String exceptionCallBackUrl;

}
