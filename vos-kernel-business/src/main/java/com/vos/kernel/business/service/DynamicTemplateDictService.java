package com.vos.kernel.business.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.vos.kernel.business.entity.DynamicTemplateDict;
import com.baomidou.mybatisplus.extension.service.IService;
import com.vos.task.automated.api.model.dto.AbilityResourceItemDTO;

/**
* <AUTHOR>
* @description 针对表【lh_dynamic_template_dict】的数据库操作Service
* @createDate 2023-10-30 11:22:27
*/
public interface DynamicTemplateDictService extends IService<DynamicTemplateDict> {

    Page<AbilityResourceItemDTO> getNewAbilityResource(Page<AbilityResourceItemDTO> abilityResource);
}
