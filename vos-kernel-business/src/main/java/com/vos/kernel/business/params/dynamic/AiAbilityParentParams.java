package com.vos.kernel.business.params.dynamic;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 该类的定义说明、使用范围
 * @Author: whz
 * @Date: Created in 2023/10/26 14:25
 * @Version:产品版本号
 */
@Data
@ApiModel(description = "能力调用统一参数")
public class AiAbilityParentParams implements Serializable {

    @ApiModelProperty(value = "actionID对应不同版本")
    private String actionId;

    @ApiModelProperty
    private String configCode;

    @ApiModelProperty(value = "待检测图片集")
    private String base64String;

    @ApiModelProperty(value = "待检测图片集")
    private String imageUrl;

    @ApiModelProperty(value = "回调地址")
    private String callBackUrl;

    @ApiModelProperty(value = "是否快慢队列，true：快队列。false:慢队列")
    private Boolean taskOrder;

    @ApiModelProperty(value = "算法额外参数")
    private JSONObject omModel;

}
