package com.vos.kernel.business.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName lh_task_ability_trend_stat
 */
@Data
public class TaskAbilityTrendStatVO implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 能力id
     */
    private Long abilityId;

    /**
     * 真实下发
     */
    private Integer assignTaskCount;

    /**
     * 预估处理
     */
    private Integer estimateCount;

    /**
     * 算法处理成功
     */
    private Integer successCount;

    /**
     * 算法处理失败
     */
    private Integer failCount;

    /**
     * 业务回调处理
     */
    private Integer callbackCount;

    /**
     * 统计时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date eventTime;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}