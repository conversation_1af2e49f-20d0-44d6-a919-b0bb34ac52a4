package com.vos.kernel.business.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName lh_dynamic_param_template
 */
@Data
public class DynamicParamTemplateDTO implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 接口名称(应用类型) 唯一约束
     */
    private String abilityName;

    /**
     * 字典表应用类型
     */
    private String dictAbilityType;

    /**
     * 描述
     */
    private String remark;

    /**
     * 应用标识 唯一约束
     */
    private String abilityTemplateId;

    /**
     * 请求算法参数模板
     */
    private String abilityTemplate;

    /**
     * 请求kernel参数模板
     */
    private String requestTemplate;

    /**
     * 1-删除 0-未删除
     */
    private Integer isDeleted;

    /**
     * 1-上架 0-下架
     */
    private Integer isGrounding;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}