package com.vos.kernel.business.workflow;

import cn.hutool.core.util.StrUtil;
import com.linker.omos.client.domain.WorkflowTaskTypeEnum;
import com.linker.omos.client.domain.request.v2workflow.TaskAddRequest;
import com.linker.omos.client.domain.response.workflow.TaskAddResponse;
import com.linker.omos.client.domain.workflow.TaskAddDTO;
import com.vos.kernel.common.workflow.ScheduleStrategyService;
import com.vos.kernel.common.workflow.TaskBuilderHolder;
import com.vos.kernel.common.workflow.TaskRespCodeEnum;
import com.vos.kernel.common.workflow.WorkflowTaskException;
import com.vos.task.manage.api.rpc.IAiAbilityCallService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年11月26日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Service
public class MessageQueueScheduleStrategyService implements ScheduleStrategyService {

    @DubboReference
    IAiAbilityCallService aiAbilityCallService;

    @Resource
    TaskBuilderHolder taskBuilderHolder;

    /**
     * 算法类型
     */
    protected List<String> type = WorkflowTaskTypeEnum.getOsControlCodes();

    @PostConstruct
    private void init() {
        for (String code : type) {
            taskBuilderHolder.registeStrategyHandler(code, this);
        }
    }


    @Override
    public TaskAddResponse addTask(TaskAddRequest taskAddRequest) {
        TaskAddResponse taskAddResponse = new TaskAddResponse();
        if (StrUtil.isBlank(taskAddRequest.getCallbackUrl())) {
            throw new WorkflowTaskException(TaskRespCodeEnum.BAD_REQUEST, TaskRespCodeEnum.BAD_REQUEST.getMessage() + ":callbackUrl");
        }
        //算法类型
        WorkflowTaskTypeEnum workflowTaskTypeEnum = WorkflowTaskTypeEnum.matchApiType(taskAddRequest.getTaskType());

        //算法大类型
        if (WorkflowTaskTypeEnum.ALGORITHM.equals(workflowTaskTypeEnum)) {
            String taskName = taskAddRequest.getTaskName();
            workflowTaskTypeEnum = WorkflowTaskTypeEnum.matchApiType(taskName);
            if (!WorkflowTaskTypeEnum.V3_CHAT.equals(workflowTaskTypeEnum) && !WorkflowTaskTypeEnum.LLM.equals(workflowTaskTypeEnum) && StrUtil.isBlank(taskAddRequest.getModel())) {
                throw new WorkflowTaskException(TaskRespCodeEnum.BAD_REQUEST, TaskRespCodeEnum.BAD_REQUEST.getMessage() + ":model");
            }
        }
        TaskAddDTO taskAddDTO = taskBuilderHolder.routeTask(workflowTaskTypeEnum.getCode()).build(taskAddRequest, workflowTaskTypeEnum);
        aiAbilityCallService.add(taskAddDTO);
        taskAddResponse.setSourceId(taskAddDTO.getAppSourceId());
        return taskAddResponse;
    }
}
