package com.vos.kernel.business.controller;

import com.linker.basic.aop.FunctionPoint;
import com.linker.basic.baseclass.BaseResp;
import com.linker.basic.enums.InterfaceType;
import com.vos.kernel.business.service.DataBackConfigManagerService;
import com.vos.kernel.data.api.model.entity.TCallBackConf;
import com.vos.kernel.core.api.vo.SwitchDeviceCallBackReq;
import com.vos.kernel.data.api.model.vo.ReflowVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.ArrayList;


/**
 * @Author: fyx
 * @Date: 2022/10/24
 * @ClassName: DataBackConfigService
 * @Description: 回流配置
 */
@RestController
@Slf4j
@Api(tags = "超管-回流配置操作接口")
@RequestMapping("/api/data/callback")
@ApiIgnore
public class DataBackConfigController{

    @Autowired
    private DataBackConfigManagerService dataBackConfigService;
    @ApiOperation("获取回流设备列表")
    @FunctionPoint(interfaceType = InterfaceType.PUBLIC)
    @GetMapping("/detialsByTenant")
    public BaseResp detialsByTenant(@ApiParam(value = "pageSize", example = "5")
                                        @RequestParam(required = false) Integer pageSize,
                                    @ApiParam(value = "pageNum", example = "1")
                                        @RequestParam(required = false) Integer pageNum,
                                    @ApiParam(value = "设备运行状态，1运行0空闲", example = "1")
                                        @RequestParam(required = false) Integer deviceStatus,
                                    @ApiParam(value = "设备名称", example = "1")
                                        @RequestParam(required = false) String deviceName,
                                    @ApiParam(value = "回流状况,1正常0关闭-1异常", example = "1")
                                        @RequestParam(required = false) Integer backStatus,
                                    @ApiParam(value = "用户唯一键", example = "10001")
                                        @RequestParam(required = false) String authenticationId) {
        return dataBackConfigService.detialsByTenant(pageNum,pageSize,deviceStatus,deviceName,backStatus,authenticationId);
    }
    @ApiOperation("按租户配置回流规则")
    @FunctionPoint(interfaceType = InterfaceType.API)
    @PostMapping("/editConfigRule")
    public BaseResp editConfigRule(@RequestBody TCallBackConf tCallBackConf) {
        return dataBackConfigService.editConfigRule(tCallBackConf);
    }


    @ApiOperation("批量开启或关闭设备回流")
    @FunctionPoint(interfaceType = InterfaceType.PUBLIC)
    @PostMapping("/switchDeviceCallBack")
    public BaseResp switchDeviceCallBack(@RequestBody BaseResp<ArrayList<SwitchDeviceCallBackReq>> data) {

        return dataBackConfigService.switchDeviceCallBack(data.getData());
    }


    @ApiOperation("获取设备回流规则")
    @FunctionPoint(interfaceType = InterfaceType.PUBLIC)
    @GetMapping("/getConfigRules")
    public BaseResp getConfigRules( @ApiParam(value = "用户唯一键", example = "1")@RequestParam(value = "authenticationId") String authenticationId,
                                        @ApiParam(value = "告警配置类型", example = "1") @RequestParam( value = "backType",required = false) String backType) {

        return dataBackConfigService.getConfigRules(authenticationId,backType);
    }

    @ApiOperation("开启关闭回流规则")
    @FunctionPoint(interfaceType = InterfaceType.PUBLIC)
    @GetMapping("/switchConfigRuleStatus")
    public BaseResp switchConfigRuleStatus(@ApiParam(value = "配置id", example = "1") @RequestParam( value = "id") Long id,
                                           @ApiParam(value = "true开false关", example = "1") @RequestParam( value = "status") Boolean status) {

        return dataBackConfigService.switchConfigRuleStatus(id,status);
    }

    @ApiOperation("初始化回流规则")
    @FunctionPoint(interfaceType = InterfaceType.PUBLIC)
    @GetMapping("/initConf")
    public BaseResp initConf( @ApiParam(value = "用户唯一键", example = "1")@RequestParam(value = "authenticationId") String authenticationId) {
        return dataBackConfigService.initConf(authenticationId);
    }

    @ApiOperation("初始化所有用户的回流规则")
    @FunctionPoint(interfaceType = InterfaceType.PUBLIC)
    @GetMapping("/initAllConf")
    public BaseResp initAllConf() {
        dataBackConfigService.checkBackConfAndInit();
        return new  BaseResp();
    }

    @ApiOperation("导出回流查看图片数量")
    @FunctionPoint(interfaceType = InterfaceType.PUBLIC)
    @PostMapping("/getDryRun")
    public BaseResp getDryRun(@RequestBody ReflowVo reflowVo) {
        return dataBackConfigService.getDryRun(reflowVo);
    }

    @ApiOperation("导出回流")
    @FunctionPoint(interfaceType = InterfaceType.PUBLIC)
    @PostMapping("/getExportInfo")
    public BaseResp getExportInfoUrl(@RequestBody ReflowVo reflowVo) {
        return dataBackConfigService.getExportInfoUrl(reflowVo);
    }

    @GetMapping("/getDiskCapacity")
    @ApiOperation("获取磁盘容量")
    public BaseResp getDiskCapacity() {
        return dataBackConfigService.getDiskCapacity();
    }

    @GetMapping("/dryRun")
    @ApiOperation("定时抽帧视频压测接口")
    public BaseResp dryRun(@RequestParam(value = "callBackId") Long callBackId, @RequestParam(value = "orgCode") String orgCode, @RequestParam(value = "startTime") String startTime,
                           @RequestParam(value = "endTime") String endTime, @RequestParam(value = "interval") Integer interval) {
        return dataBackConfigService.dryRun(callBackId,orgCode,startTime,endTime,interval);
    }
}
