package com.vos.kernel.business.grpc;

import com.vos.kernel.business.config.WorkflowProperties;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025年05月26日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Configuration
@AllArgsConstructor
@EnableConfigurationProperties(WorkflowProperties.class)
@ConditionalOnProperty(name = "workflow.enableConductorGrpc", matchIfMissing = true)
public class ConductorGrpcConfig {

    @Bean
    public ConductorTaskGrpcClient conductorTaskGrpcClient( WorkflowProperties workflowProperties) {
        return new ConductorTaskGrpcClient(workflowProperties);
    }
}
