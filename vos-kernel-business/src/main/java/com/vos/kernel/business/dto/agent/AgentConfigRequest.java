package com.vos.kernel.business.dto.agent;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025年05月15日
 * @version: 1.0
 * @description: TODO
 */
@Data
public class AgentConfigRequest implements Serializable {

    @NotBlank(message = "agentId不能为空")
    private String agentId;

    /**
     * 是否官方推荐
     */
    private Boolean isOfficial = false;

    /**
     * 官方推荐顺序
     */
    private Integer officialOrder = 0;


    /**
     * 视频地址
     */
    private String videoUrl;
}
