package com.vos.kernel.business.authentication;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.net.URLDecoder;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.linker.basic.exception.BusinessException;
import com.linker.core.utils.cnsign.SM4Util;
import com.vos.kernel.business.dto.HubEnterpriseItem;
import com.vos.kernel.business.entity.UserContext;
import com.vos.kernel.business.entity.UserVo;
import com.vos.kernel.business.exception.TokenException;
import com.vos.kernel.business.service.IOmhubAsyncService;
import com.vos.kernel.business.service.TUserManagerService;
import com.vos.kernel.common.dubbo.DubboDataContext;
import com.vos.kernel.core.api.domain.TUserAuthentication;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年05月06日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Component
public class GatewayInterceptor {


    @Resource
    TUserManagerService tUserManagerService;


    @Resource
    IOmhubAsyncService omhubAsyncService;

    List<String> URLS = Arrays.asList("/v1/query/hub/akSk", "/service/app/manage/install", "/service/tTask/fileTaskUpload", "/calculate/sendToAiForEvent", "/server/vector/dataToVector", "/calculate/sendToV3AiAsync", "/service/sendToAi");



    /**
     * 校验 Authorization: Bearer
     *
     * @param request
     * @param response
     * @param handler
     * @return
     * @throws IOException
     */
    public boolean validateToken(HttpServletRequest request, HttpServletResponse response, Object handler) throws IOException {

        String token = request.getHeader("x-linker-rpc-userinfo");
        try {
            String decodedJson = URLDecoder.decode(token, StandardCharsets.UTF_8);
            UserContext userInfo = JSON.parseObject(decodedJson, UserContext.class);
            //非超管检验授权文件
            UserVo user = userInfo.getUser();
            UserContext.TenantInfoDTO tenantInfoDTO = userInfo.getTenantInfoDTO();

            HubEnterpriseItem hubEnterpriseItem = new HubEnterpriseItem();
            hubEnterpriseItem.setIsAdmin(true);
            if (user == null || (!"1".equals(user.getRoleTypes()) && !"2".equals(user.getRoleTypes()))) {
                hubEnterpriseItem.setIsAdmin(false);
                //校验licence
                boolean valid = false;
                //一些特定的请求接口不能调用
                if (CollectionUtil.isNotEmpty(URLS) && URLS.contains(request.getRequestURI())) {
                    valid = true;
                }
                omhubAsyncService.checkLicence(token, tenantInfoDTO.getTenantId(), valid, true);
            }
            //AkSk
            String ak = tenantInfoDTO.getAppKey();
            TUserAuthentication tUserAuthentication = tUserManagerService.getUserInfoByTenantId(tenantInfoDTO.getTenantId());
            if (ObjectUtil.isNull(tUserAuthentication)) {
                String sk = SM4Util.decryptDataECB(tenantInfoDTO.getAppSecret());
                //同步
                hubEnterpriseItem.setAppKey(ak);
                hubEnterpriseItem.setAppSecret(sk);
                hubEnterpriseItem.setTenantName(tenantInfoDTO.getTenantName());
                hubEnterpriseItem.setTenantId(tenantInfoDTO.getTenantId());
                omhubAsyncService.translatedAkSkGateWay(hubEnterpriseItem);
                tUserAuthentication = tUserManagerService.getUserInfoByTenantId(tenantInfoDTO.getTenantId());
                if (ObjectUtil.isNull(tUserAuthentication)) {
                    ThreadUtil.safeSleep(100);
                    tUserAuthentication = tUserManagerService.getUserInfoByTenantId(tenantInfoDTO.getTenantId());
                }
            }
            DubboDataContext.setAppIdHolder(tUserAuthentication.getAppId());
            DubboDataContext.setAppKeyHolder(ak);
            DubboDataContext.setAuthIdHolder(tenantInfoDTO.getTenantId());
            DubboDataContext.setUserHolder(user == null ? null : user.getUserCode());
            request.setAttribute(com.vos.kernel.business.authentication.UserContext.class.getName(), userInfo);
        }catch (BusinessException e){
            log.error("gateway异常",e);
            throw e;
        } catch (Exception e) {
            log.error("token解析异常:{}", token,e);
            throw new TokenException("网关token解析异常,认证失败");
        }

        return true;
    }
    
}
