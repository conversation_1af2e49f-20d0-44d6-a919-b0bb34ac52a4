#FROM hbt.linker.cc/base/openjdk:8u402-jdk-alpine-skyagent
FROM registry.linker.cc/vos/openjdk:jdk-fabric8-withsky
ENV TZ=Asia/Shanghai
WORKDIR /usr/local/
ARG CI_PROJECT_NAME
ARG PASSWORD
ADD ${CI_PROJECT_NAME}.jar app.jar
EXPOSE 8081

RUN mkdir -p /usr/local/logs && chmod 777 /usr/local/logs

RUN echo "java -javaagent:/usr/local/app.jar=\"-pwd ${PASSWORD}\" \
-javaagent:/usr/local/skywalking-agent/skywalking-agent.jar \
-Dskywalking.agent.is_cache_enhanced_class=true \
-Dskywalking.agent.class_cache_mode=FILE \
-Dskywalking.agent.service_name=ilink::${CI_PROJECT_NAME}-service \
-Dskywalking.agent.keep_tracing=true \
-Dskywalking.collector.backend_service=\${SKYWALKING_SERVICE} \
-jar \
\${JAVA_OPTS} \
-XX:MaxRAMPercentage=80.0 \
-XX:+PrintGC \
-XX:+PrintGCDetails \
-Xloggc:/usr/local/logs/gc.log \
-XX:ErrorFile=/usr/local/logs/hs_err_pid.log \
-XX:+HeapDumpOnOutOfMemoryError \
-XX:HeapDumpPath=/usr/local/logs/jvmlog \
app.jar \
--logging.config=/usr/local/config/logback_nacos.xml -Dspring.config.location=/usr/local/config/*.yaml" > /run.sh && chmod 777 /run.sh
ENTRYPOINT ["/bin/sh","/run.sh"]