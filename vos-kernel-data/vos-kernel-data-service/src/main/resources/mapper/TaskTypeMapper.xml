<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vos.kernel.data.service.mapper.TaskTypeMapper">

    <select id="getSubTaskByTypeCodeList" parameterType="java.lang.Integer" resultType="com.vos.kernel.data.api.model.entity.TaskSub">
        SELECT
        name,
        ability,
        task_type_code taskTypeCode,
        id type
        FROM
        t_task_type
        where task_type_code in
        <foreach collection="typeCodeList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getAbilityId" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT
        ta.operator_id
        FROM
        t_task_type t1
        INNER JOIN tb_ability ta ON ta.ability_id = t1.ability and ta.is_del = 0
        where t1.isdel = 0 and t1.task_type_code = #{taskTypeCode}
    </select>
    <select id="getPodLimitByTaskType" resultType="java.lang.Long">
        SELECT
            tua.pod_limit
        FROM
            t_task_type t1
                LEFT JOIN t_user_authentication tua on t1.orgcode = tua.authentication_id
        where t1.isdel = 0 and t1.task_type_code = #{taskTypeCode}
    </select>
</mapper>
