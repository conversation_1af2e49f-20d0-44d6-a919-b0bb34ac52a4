package com.vos.kernel.data.service.service.impl.deviceDock;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.vos.kernel.common.enums.WebDavExportEnum;
import com.vos.kernel.common.storage.StorageBaseComponent;
import com.vos.kernel.common.storage.WebDavCommonConfigProperties;
import com.vos.kernel.common.constant.RedisKey;
import com.vos.kernel.data.api.model.req.SaveYiShi;
import com.vos.kernel.data.api.rpc.DataInfoVideoService;
import com.vos.kernel.data.service.service.impl.WebDavService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 易时一体机资源目录接入业务
 */
@Service
@Slf4j
public class YiShiService {
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    @Lazy
    private DataInfoVideoService videoService;

    @Resource
    WebDavService webDavService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Value("${yishi.basePath}")
    private String basePath;

    @Resource
    WebDavCommonConfigProperties webDavCommonConfigProperties;
    @Resource
    StorageBaseComponent storageComponent;

    /**
     * 拍照
     */
    public Map<String, String> tokePhoto(SaveYiShi saveYiShi, Boolean delFlag, Long videoId) {
        Map<String, String> map = new HashMap<>();

        File file = new File(saveYiShi.getDeviceSerial());
        File[] files = file.listFiles();
        List<File> list = Arrays.stream(Optional.ofNullable(files).orElse(new File[0])).collect(Collectors.toList());
        if (CollUtil.isEmpty(list)) {
            return map;
        }

        List<String> fileNameList = new ArrayList<>();
        String fileName = "";
        for (File v : list) {
            try {
                if (v.isFile() || v.getCanonicalFile().isFile()) {
                    fileName = v.getName();
                    if (delFlag) {
                        break;
                    }
                    fileNameList.add(fileName);
                } else if (v.isDirectory()) {
                    File[] lowFiles = v.listFiles();
                    List<File> lowFile = Arrays.stream(Optional.ofNullable(lowFiles).orElse(new File[0])).collect(Collectors.toList());
                    if (CollUtil.isEmpty(lowFile)) {
                        FileUtil.del(v);
                        continue;
                    }

                    for (File v1 : lowFile) {
                        try {
                            if (v1.isFile() || v1.getCanonicalFile().isFile()) {
                                fileName = v.getName() + "/" + v1.getName();
                                if (delFlag) {
                                    break;
                                }
                                fileNameList.add(fileName);
                            } else if (v1.isDirectory()) {
                                FileUtil.del(v1);
                            }
                        } catch (IOException e) {
                            log.warn("目录路径异常");
                        }
                    }
                }
            } catch (IOException e) {
                log.warn("目录路径异常");
            }
        }

        if (StrUtil.isBlank(fileName)) {
            return map;
        }

        //Long id = SnowFlakeUtil.getId();
        //String[] splitFile = fileName.split("\\.");
        //String fileType = splitFile[splitFile.length - 1];
        if (delFlag) {
            String url = saveYiShi.getMapperUrl() + "/" + fileName;
            if (checkMapperUrl(url)) {
                File yishiFile = new File(url);
                if (yishiFile.exists()) {
                    return null;
                }
                String path = "excuted/" + videoId + "/" + fileName;
                String movePath = webDavService.moveWebDav(storageComponent.wabDavProxyReverse(url, WebDavExportEnum.GUI.getCode()), path);
                log.info("移动扫盘路径：{}，{}", url, path);
                map.put("outOss", movePath);
            }
        } else {
            if (CollUtil.isNotEmpty(fileNameList)) {
                String s = stringRedisTemplate.opsForValue().get(RedisKey.YISHI_COUNT + videoId);
                if (StrUtil.isNotBlank(s) && fileNameList.contains(s) && fileNameList.indexOf(s) < fileNameList.size() - 1) {
                    fileName = fileNameList.get(fileNameList.indexOf(s) + 1);
                } else {
                    fileName = fileNameList.get(0);
                }
                String url = saveYiShi.getMapperUrl() + "/" + fileName;
                if (checkMapperUrl(url)) {
                    File tempFile = new File(saveYiShi.getDeviceSerial() + "/" + fileName);
                    File tFile = new File(webDavCommonConfigProperties.getHome() + "/");
                    if (!tFile.exists()) {
                        tFile.mkdir();
                    }
                    File temFile = new File(webDavCommonConfigProperties.getHome() + fileName);
                    try {
                        FileUtil.copy(tempFile, temFile, true);
                    } catch (IORuntimeException e) {
                        log.warn("复制失败!  {}", e.getMessage());
                        return map;
                    }
                    map.put("outOss", storageComponent.wabDavProxy(saveYiShi.getMapperUrl() + "/" + fileName, WebDavExportEnum.GUI.getCode()));
                }
                stringRedisTemplate.opsForValue().set(RedisKey.YISHI_COUNT + videoId, fileName);
            }
        }
        return map;
    }

    public static void main(String[] args) {
        File yishiFile = new File("D:\\data\\aa.jpg");
        System.out.println(yishiFile.exists());
    }

    private Boolean checkMapperUrl(String mapperUrl) {
        mapperUrl = URLUtil.encode(mapperUrl);
        try {
            URL url = new URL(mapperUrl);
            URLUtil.getStream(url);
            return true;
        } catch (Exception e) {
            log.warn("mapperUrl校验不通过--->{}", e.getMessage());
            return false;
        }
    }
}
