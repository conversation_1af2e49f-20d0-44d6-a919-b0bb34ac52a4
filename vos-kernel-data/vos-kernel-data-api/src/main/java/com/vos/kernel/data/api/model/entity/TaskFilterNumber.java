package com.vos.kernel.data.api.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_task_filter_number")
@ApiModel(value="TaskFilterNumber对象", description="")
public class TaskFilterNumber implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "机构id")
    private String orgId;

    @ApiModelProperty(value = "应用id")
    private Long taskTypeId;

    @ApiModelProperty(value = "汇目云应用id")
    private Long muAiType;

    @ApiModelProperty(value = "处理时间 年月日")
    private LocalDate handleTime;

    @ApiModelProperty(value = "小时")
    private Integer hour;

    @ApiModelProperty(value = "前置过滤数（AI加速+前置过滤样本比对+免告警时段+坏帧过滤）")
    private Long preFilterNum;

    @ApiModelProperty(value = "后置过滤数(参数配置中除了前置过滤之外的过滤类型）")
    private Long postFilterNum;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;


}
