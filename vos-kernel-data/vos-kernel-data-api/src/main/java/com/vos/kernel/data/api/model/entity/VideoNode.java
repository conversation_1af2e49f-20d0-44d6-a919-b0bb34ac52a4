package com.vos.kernel.data.api.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 视频流节点表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_video_node")
@ApiModel(value="VideoNode对象", description="视频流节点表")
public class VideoNode implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "节点名称")
    private String nodeName;

    @ApiModelProperty(value = "深度")
    private Integer level;

    @ApiModelProperty(value = "序号")
    private Integer sort;

    @ApiModelProperty(value = "父节点id")
    private Long parentNodeId;

    @ApiModelProperty(value = "备注")
    private String description;

    @ApiModelProperty(value = "机构id")
    private String orgId;

    @ApiModelProperty(value = "是否删除(0未删除 1已删除)")
    private Integer isDel;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


}
