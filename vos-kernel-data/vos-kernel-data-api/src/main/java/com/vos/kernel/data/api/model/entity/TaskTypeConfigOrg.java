package com.vos.kernel.data.api.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_task_type_config_org")
@ApiModel(value="TaskTypeConfigOrg对象", description="")
public class TaskTypeConfigOrg implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "算子id")
    private String taskTypeId;

    @ApiModelProperty(value = "汇目云算子id")
    private Long muTypeId;

    @ApiModelProperty(value = "机构id")
    private String orgcode;

    @ApiModelProperty(value = "置信度")
    private String believe;

    @ApiModelProperty(value = "是否显示置信度")
    private Integer displayBelieve;

    @ApiModelProperty(value = "是否显示参考框")
    private Integer displayBox;

    @ApiModelProperty(value = "告警宽度过滤值")
    private Integer shieldValueW;

    @ApiModelProperty(value = "告警高度过滤值")
    private Integer shieldValueH;

    @ApiModelProperty(value = "告警宽度过滤值(大)")
    private Integer shieldValueHighW;

    @ApiModelProperty(value = "告警高度过滤值(大)")
    private Integer shieldValueHighH;

    @ApiModelProperty(value = "目标覆盖")
    private String target;

    @ApiModelProperty(value = "回避策略")
    private String avoid;

    @ApiModelProperty(value = "包含策略")
    private String include;

    @ApiModelProperty(value = "高级配置")
    private String config;

    @ApiModelProperty(value = "正向协同")
    private String forward;

    @ApiModelProperty(value = "反向协同")
    private String reverse;

    @ApiModelProperty(value = "矩形放大率")
    private String magnify;

    @ApiModelProperty(value = "过滤验证")
    private String filterVerify;

    @ApiModelProperty(value = "包含验证")
    private String includeVerify;

    @ApiModelProperty(value = "是否删除(0未删除 1已删除)")
    private Integer isDel;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "忽略告警时间列表")
    private String ignoreTimeList;

    @ApiModelProperty(value = "连续告警次数")
    private Integer alarmIgnoreNum;


}
