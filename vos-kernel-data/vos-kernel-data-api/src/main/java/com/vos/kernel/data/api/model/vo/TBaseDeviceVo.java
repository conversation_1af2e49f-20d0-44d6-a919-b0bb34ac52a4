package com.vos.kernel.data.api.model.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * @date 2021-09-16 10:12:04
 */
@Data
@Accessors(chain = true)
public class TBaseDeviceVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 平台账号
     */
    private String username;

    /**
     * 平台密码
     */
    private String password;

    /**
     * 区域代码
     */
    private String cameraId;

    /**
     *  码率
     */
    private String streamType;

    /**
     * 华为云实时流api地址
     */
    private String liveUrl;
}
