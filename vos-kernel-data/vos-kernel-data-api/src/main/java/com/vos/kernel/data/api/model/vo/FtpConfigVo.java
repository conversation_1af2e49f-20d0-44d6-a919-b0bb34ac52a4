package com.vos.kernel.data.api.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 *
 */
@Data
@Accessors(chain = true)
public class FtpConfigVo implements Serializable {
    /**
     * 设备名称
     */
    private String setName;
    /**
     * 分组id
     */
    private Long classifyId;
    /**
     * 主机
     */
    private String deviceSerial;
    /**
     * 端口
     */
    private Integer port;
    /**
     * 用户名
     */
    private String ak;
    /**
     * 密码
     */
    private String sk;
    /**
     * 文件夹路径
     */
    private String mapperUrl;
    /**
     * 协议类型
     */
    private String subClass;
    //用户唯一标识
    @ApiModelProperty(value = "用户唯一标识")
    String authenticationId;
}