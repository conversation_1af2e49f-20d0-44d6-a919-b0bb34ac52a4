package com.vos.kernel.data.api.model.enumPackage;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 厂商枚举类型
 */
@AllArgsConstructor
@Getter
public enum CompanyTypeEnum {
    /**
     * 公有云
     */
    GYY("公有云",1),

    /**
     * 私有云
     */
    SYY("私有云",2),

    /**
     * 其他
     */
    QT("其他",3),

    /**
     * 设备直连
     */
    SBZL("设备直连",4);

    private String companyType;

    private Integer code;
}
