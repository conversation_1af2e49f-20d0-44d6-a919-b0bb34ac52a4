package com.vos.kernel.data.api.model.enumPackage;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务状态类型枚举
 */
@Getter
@AllArgsConstructor
public enum OpTypeEnum {
    /**
     * 任务创建
     */
    TASK_CREAT(1),
    /**
     * 任务开启
     */
    TASK_START(2),
    /**
     * 任务停止
     */
    TASK_STOP(3),
    /**
     * 任务删除
     */
    TASK_DEL(4);

    private final Integer typeCode;
}
