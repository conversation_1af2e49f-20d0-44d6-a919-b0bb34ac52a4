syntax = "proto3";

option java_multiple_files = true;
option java_package = "com.vos.kernel.common.grpc";
option java_outer_classname = "WorkflowTaskProto";

package workflow;

// 导入 Google 的 timestamp 类型
import "google/protobuf/timestamp.proto";

// 任务添加请求
message GrpcTaskAddRequest {
  int64 wait_time = 1;//任务下发后多长时间有效:单位毫秒
  string task_type = 2;//任务类型
  string task_name = 3;//模型类型｜workerName不能为空
  string task_biz_meta = 4;//业务元数据,透传返回
  string task_params = 5;//任务执行所需的具体参数 json 格式
  string domain = 6;// 自定义worker域名
  string model = 7;           // 模型ID
  string callback_url = 8;    // 任务完成后的回调地址
}

// 任务添加响应
message GrpcTaskAddResponse {
  string task_id = 1;
  bool success = 2;
  string message = 3;
}

// 任务拉取请求
message GrpcTaskPollRequest {
  string task_def_name = 1;//任务名称
  string domain = 2;// 自定义worker域名
  bool is_prod = 3;//是否是生产环境
  bool is_public = 4;//是否共用
  int32 batch_size = 5;//批次
  string worker_id = 6;
  string secret = 7;           // 模型ID
}



// 任务状态更新请求
message GrpcTaskUpdateRequest {
  string task_id = 1;//任务唯一标识不能为空
  string status = 2;//状态
  string reason_for_in_completion = 3;//失败原因
  string worker_id = 4;       // worker ID
  string task_biz_meta = 5;        // 业务元数据
  string callback_url = 6;    // 回调地址
  repeated TaskExecLogs logs = 7;            // 执行日志
  string output_data = 8;     // 输出数据
}

message TaskExecLogs {
  string log = 1;
  string task_id = 2;
  int64 created_time = 3;
}

// 任务状态更新响应
message GrpcTaskUpdateResponse {
  bool success = 1;
  string message = 2;
}

// 批量拉取任务响应
message GrpcBatchPollResponse {
  repeated GrpcTaskMessage tasks = 1;
}

// 任务消息
message GrpcTaskMessage {
  string task_id = 1;//任务ID
  int64 wait_time = 2;//任务下发后多长时间有效
  string task_payload = 3;//任务执行所需的具体参数
  string task_biz_meta = 4;        // 业务元数据
  string callback_url = 5;    // 回调地址
}

// WorkflowTask 服务定义
service WorkflowTaskService {
  // 添加任务
  rpc AddTask(GrpcTaskAddRequest) returns (GrpcTaskAddResponse);
  
  // 批量拉取任务
  rpc BatchPoll(GrpcTaskPollRequest) returns (GrpcBatchPollResponse);
  
  // 更新任务状态
  rpc UpdateTaskStatus(GrpcTaskUpdateRequest) returns (GrpcTaskUpdateResponse);
} 