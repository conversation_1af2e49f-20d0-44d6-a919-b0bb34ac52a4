package com.linker.omos.client.config;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.support.spring.messaging.MappingFastJsonMessageConverter;
import com.linker.omos.client.component.IWorkflowTaskClient;
import com.linker.omos.client.domain.Response;
import com.linker.omos.client.domain.WorkflowStatusEnum;
import com.linker.omos.client.domain.request.v2workflow.TaskMessage;
import com.linker.omos.client.domain.request.v2workflow.TaskPollRequest;
import com.linker.omos.client.domain.request.v2workflow.TaskUpdateRequest;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.messaging.support.MessageBuilder;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @date 2024年12月12日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@SuppressWarnings("WeakerAccess")
@Data
public class DefaultWorkflowTaskListenerContainer implements InitializingBean, DisposableBean {


    /**
     * 容器名称
     */
    private String name;

    /**
     * 对应注解信息
     */
    private WorkflowTaskMessageListener workflowTaskMessageListener;

    @Override
    public void destroy() throws Exception {
        stop();
    }


    /**
     * 监听消息队列名称
     */
    private String streamQueue;

    /**
     * 访问领域
     */
    private String domain;

    /**
     * 消费线程数
     */
    private Integer consumeThreadNumber;


    /**
     * 拉取批次大小
     */
    private Integer pollBatchSize;

    /**
     * 拉取间隔
     */
    private long pollingInterval;


    /**
     * 拉取间隔
     */
    private long pollTimeoutInMs;


    /**
     * 被代理类
     */
    private WorkflowTaskListener workflowTaskListener;

    /**
     * 定时线程
     */
    private ScheduledExecutorService scheduledExecutorService;

    /**
     * 任务客户端
     */
    private IWorkflowTaskClient taskClient;


    /**
     * 是否运行
     */
    private boolean running;

    /**
     * 请求线程
     */
    private ThreadPoolExecutor executorService;

    /**
     * 并发控制
     */
    private ConcurrentSemaphore concurrentSemaphore;

    /**
     * 优雅关闭
     */
    private Integer shutdownGracePeriodSeconds = 10;

    /**
     * 消息转换器
     */
    private MappingFastJsonMessageConverter messageConverter;

    /**
     * 任务类型
     */
    private Type taskType;

    /**
     * workerId
     */
    private String workerId;

    /**
     * 是否生产环境
     */
    private Boolean isProd;

    private Boolean isPublic;

    /**
     * 是否关闭
     */
    private Boolean closed;

    /**
     * 拉取空次数
     */
    private volatile AtomicLong pollEmptyNum = new AtomicLong(0);

    /**
     * 运行中的任务
     * key: taskId
     * value: Dict
     */
    private volatile ConcurrentHashMap<String, Dict> runningTask = new ConcurrentHashMap<>(32);
    /**
     * 拉取空次数
     */

    /**
     * 初始化配置
     *
     * @param anno
     */
    public void setMessageListenerProperties(WorkflowTaskMessageListener anno) {
        this.workflowTaskMessageListener = anno;
        this.streamQueue = anno.taskDefName();
        this.domain = anno.domain();
        this.consumeThreadNumber = anno.consumeThreadNumber();
        this.pollBatchSize = anno.pollBatchSize();
        this.pollingInterval = anno.pollingInterval();
        this.pollTimeoutInMs = anno.pollTimeoutInMs();
        this.messageConverter = new MappingFastJsonMessageConverter();
    }

    /**
     * 设置被代理类
     *
     * @param workflowTaskListener
     */
    public void setWorkflowTaskListener(WorkflowTaskListener workflowTaskListener) {
        this.workflowTaskListener = workflowTaskListener;
    }

    /**
     * 设置名称
     *
     * @param name
     */
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.taskType = getTaskTrueType();
        log.debug("taskType: {}", taskType);
    }

    /**
     * 获取方法参数类型
     *
     * @return
     */
    private Type getTaskTrueType() {
        Class<?> targetClass;
        targetClass = AopProxyUtils.ultimateTargetClass(workflowTaskListener);
        Type matchedGenericInterface = null;
        while (Objects.nonNull(targetClass)) {
            Type[] interfaces = targetClass.getGenericInterfaces();
            for (Type type : interfaces) {
                if (type instanceof ParameterizedType &&
                        (Objects.equals(((ParameterizedType) type).getRawType(), WorkflowTaskListener.class))) {
                    matchedGenericInterface = type;
                    break;
                }
            }
            targetClass = targetClass.getSuperclass();
        }
        if (Objects.isNull(matchedGenericInterface)) {
            return Object.class;
        }

        Type[] actualTypeArguments = ((ParameterizedType) matchedGenericInterface).getActualTypeArguments();
        if (Objects.nonNull(actualTypeArguments) && actualTypeArguments.length > 0) {
            return actualTypeArguments[0];
        }
        return Object.class;
    }

    /**
     * 开启任务
     */
    public void start() {
        if (this.isRunning()) {
            throw new IllegalStateException("workflowTask 队列消息监听 already running. " + this.streamQueue);
        }
        //定时拉取并处理消息
        this.setRunning(true);

        //定时拉取处理消息
        this.scheduledExecutorService.scheduleAtFixedRate(this::pollAndExecute,
                this.pollingInterval,
                this.pollingInterval,
                TimeUnit.MILLISECONDS);

        this.scheduledExecutorService.scheduleAtFixedRate(this::logRunningTask,
                1,
                1,
                TimeUnit.SECONDS);
        log.info("running container: {}", this.streamQueue);
    }


    /**
     * 关闭任务
     */
    public void stop() {
        log.info("workflowTask 队列消息监听 destroyed, {}", this.streamQueue);
        if (this.isRunning()) {
            if (Objects.nonNull(scheduledExecutorService)) {
                scheduledExecutorService.shutdown();
            }
            setRunning(false);
            if (this.executorService != null) {
                try {
                    executorService.shutdown();
                    if (executorService.awaitTermination(this.shutdownGracePeriodSeconds, TimeUnit.SECONDS)) {
                        log.debug("tasks completed, shutting down");
                    } else {
                        log.warn(String.format("forcing shutdown after waiting for %s second", this.shutdownGracePeriodSeconds));
                        executorService.shutdownNow();
                    }
                } catch (InterruptedException ie) {
                    log.warn("shutdown interrupted, invoking shutdownNow");
                    executorService.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
        }
    }


    /**
     * 获取运行状态
     *
     * @return
     */
    public boolean isRunning() {
        return this.running;
    }

    /**
     * 设置运行状态
     *
     * @param running
     */
    private void setRunning(boolean running) {
        this.running = running;
    }

    /**
     * 初始化
     *
     * @param taskClient
     * @param taskProperties
     */
    public void init(String workerId, IWorkflowTaskClient taskClient, WorkflowTaskProperties.TaskProperties taskProperties, Integer shutdownGracePeriodSeconds, Boolean isProd, Boolean isPublic) {
        log.info("workflowTask 队列消息监听 init, {}", this.streamQueue);
        this.isProd = isProd;
        this.isPublic = isPublic;
        this.taskClient = taskClient;
        this.workerId = workerId;
        this.closed = false;
        this.scheduledExecutorService = Executors.newScheduledThreadPool(2);
        this.shutdownGracePeriodSeconds = shutdownGracePeriodSeconds;

        if (null != taskProperties) {
            if (taskProperties.getIsPublic() != null) {
                this.isPublic = taskProperties.getIsPublic();
            }
            if (BooleanUtil.isTrue(taskProperties.getClosed())) {
                this.closed = true;
            }
            if (StrUtil.isNotBlank(taskProperties.getDomain())) {
                this.domain = taskProperties.getDomain();
            }
            if (null != taskProperties.getThreadNum()) {
                this.consumeThreadNumber = taskProperties.getThreadNum();
            }
            if (null != taskProperties.getPollBatchSize()) {
                this.pollBatchSize = taskProperties.getPollBatchSize();
            }
            if (null != taskProperties.getPollingInterval()) {
                this.pollingInterval = taskProperties.getPollingInterval();
            }

            if (StrUtil.isNotBlank(taskProperties.getTaskAasId())) {
                this.streamQueue = taskProperties.getTaskAasId();
                log.info("workflowTask 队列消息监听 替换平台任务标识, {}", this.streamQueue);
            }
        }

        ThreadFactory build = new ThreadFactoryBuilder().setNamePrefix("workflowTask-" + this.streamQueue + "-pool-%d").build();
        //请求线程池
        this.executorService = ExecutorBuilder.create()
                .setCorePoolSize(this.consumeThreadNumber)
                .setMaxPoolSize(this.consumeThreadNumber + 32)
                .useArrayBlockingQueue(1000)
                .setThreadFactory(build)
                .build();
        //并发控制
        this.concurrentSemaphore = new ConcurrentSemaphore(this.consumeThreadNumber);
    }

    /**
     * 获取休眠时间
     *
     * @return
     */
    private long getSleepTime() {
        long sleepTime = this.pollingInterval < 20 ? 20 : 0;
        long l = pollEmptyNum.get();
        if (l > 10 && l < 100) {
            sleepTime = this.pollingInterval;
        } else if (l >= 100 && l < 500) {
            sleepTime = this.pollingInterval * 2;
        } else if (l >= 500) {
            sleepTime = this.pollingInterval * 4;
        }
        if (l > 1000 && sleepTime < 1000) {
            sleepTime = 3000;
        }
        //最大10s
        if (sleepTime > 10000) {
            sleepTime = 10000;
        }
        return sleepTime;
    }

    /**
     * 打印运行中的任务
     */
    private void logRunningTask() {
        log.info("task of type: {},运行中任务数：{},详情：{}", streamQueue, this.runningTask.size(), this.runningTask);
    }

    /**
     * 拉取并处理消息
     */
    private void pollAndExecute() {
        //判断并发是否充足
        int slotsToAcquire = concurrentSemaphore.availableSlots();
        if (slotsToAcquire <= 0 || !concurrentSemaphore.acquireSlots(streamQueue, slotsToAcquire)) {
            log.debug("Polling task of type: {} 并发已用完，{}", streamQueue, slotsToAcquire);
            return;
        }

        int acquiredTasks = 0;
        try {
            log.debug("Polling task of type: {} in domain: '{}'", streamQueue, domain);
            long sleepTime = getSleepTime();
            if (sleepTime > 0) {
                ThreadUtil.safeSleep(sleepTime);
            }
            //批量获取数据定额数量的任务
            TaskPollRequest taskPollRequest = new TaskPollRequest();
            taskPollRequest.setTaskDefName(streamQueue);
            taskPollRequest.setDomain(domain);
            taskPollRequest.setWorkerId(workerId);
            taskPollRequest.setIsProd(isProd);
            taskPollRequest.setIsPublic(isPublic);
            taskPollRequest.setBatchSize(slotsToAcquire);

            Response<List<TaskMessage>> tasksResult = taskClient.batchPoll(taskPollRequest);
            if (tasksResult != null) {
                if (tasksResult.getCode() != 200) {
                    log.error("Error when polling for tasks :{}", StrUtil.isBlank(tasksResult.getMessage()) ? "请检查workflow配置" : tasksResult.getMessage());
                }
                if (CollectionUtil.isNotEmpty(tasksResult.getData())) {
                    pollEmptyNum.set(0);
                    List<TaskMessage> tasks = tasksResult.getData();
                    acquiredTasks = tasks.size();
                    // 处理任务
                    for (TaskMessage task : tasks) {
                        if (Objects.nonNull(task) && StringUtils.isNotBlank(task.getId())) {
                            log.debug("Polled task: {} of type: {} in domain: '{}'", task.getId(), streamQueue, domain);
                            this.executorService.execute(RunnableWrapper.of(() -> processTask(task, concurrentSemaphore)));
                        } else {
                            // no task was returned in the poll, release the permit
                            concurrentSemaphore.complete(1);
                        }
                    }
                } else {
                    pollEmptyNum.incrementAndGet();
                }
            }
        } catch (Exception e) {
            log.error("Error when polling for tasks", e);
        }

        //释放并发
        concurrentSemaphore.complete(slotsToAcquire - acquiredTasks);
    }


    /**
     * 处理任务
     *
     * @param task
     * @param concurrentSemaphore
     */
    private void processTask(TaskMessage task, ConcurrentSemaphore concurrentSemaphore) {

        long l = System.currentTimeMillis();
        log.info("begin Executing task: {} of type: {} in worker: {}", task.getId(), streamQueue, workflowTaskListener.getClass().getSimpleName());
        try {
            runningTask.put(task.getId(), Dict.create().set("startTime", l));
            executeTask(task);
        } finally {
            runningTask.remove(task.getId());
            log.info("end Executing task: {} of type: {} in worker: {},cost:{}ms", task.getId(), streamQueue, workflowTaskListener.getClass().getSimpleName(), System.currentTimeMillis() - l);
            concurrentSemaphore.complete(1);
        }
    }

    /**
     * 类型转换
     *
     * @param payload
     * @return
     */
    private Object doConvertMessage(String payload) {

        if (Objects.equals(taskType, String.class)) {
            return payload;
        } else {
            try {
                if (taskType instanceof Class) {
                    return this.messageConverter.fromMessage(MessageBuilder.withPayload(payload).build(), (Class<?>) taskType);
                } else {
                    throw new RuntimeException("cannot convert message to " + taskType);
                }
            } catch (Exception e) {
                log.info("convert failed. str:{}, msgType:{}", payload, taskType);
                throw new RuntimeException("cannot convert message to " + taskType, e);
            }
        }
    }

    /**
     * 处理任务
     *
     * @param task
     */
    private void executeTask(TaskMessage task) {
        StopWatch stopwatch = new StopWatch();
        stopwatch.start();
        TaskUpdateRequest result = new TaskUpdateRequest();
        result.setTaskId(task.getId());
        result.setWorkerId(workerId);
        result.setBizMeta(task.getBizMeta());
        result.setCallbackUrl(task.getCallbackUrl());
        WorkflowTaskContext workflowTaskContext = new WorkflowTaskContext(task.getId());
        try {
            //执行任务
            String payload = task.getPayload();
            log.info("payload: {}", payload);
            if (StrUtil.isNotBlank(task.getBizMeta())) {
                JSONObject jsonObject = JSONObject.parseObject(task.getBizMeta());
                workflowTaskContext.setTaskId(jsonObject.getString("taskId"));
                workflowTaskContext.setWorkflowInstanceId(jsonObject.getString("workflowInstanceId"));
                workflowTaskContext.setContextParams(jsonObject.getJSONObject("context"));
            }
            Object o = workflowTaskListener.onTask(doConvertMessage(payload), workflowTaskContext);
            result.setLogs(workflowTaskContext.getLogs());
            result.setStatus(WorkflowStatusEnum.COMPLETED);
            result.setOutputData(o);
        } catch (Exception e) {
            result.setLogs(workflowTaskContext.getLogs());
            log.error("Unable to execute task: {} of type: {},{}", task.getId(), streamQueue, e.getMessage(), e);
            result.setStatus(WorkflowStatusEnum.FAILED);
            result.setReasonForIncompletion(StrUtil.isBlank(e.getMessage()) ? "执行异常" : e.getMessage());
        } finally {
            stopwatch.stop();
        }
        log.debug("Task: {} executed by worker: {} at {} with status: {}", task.getId(), workflowTaskListener.getClass().getSimpleName(), stopwatch.getTime(), result.getStatus());
        taskClient.updateTaskStatus(result);
    }
}
