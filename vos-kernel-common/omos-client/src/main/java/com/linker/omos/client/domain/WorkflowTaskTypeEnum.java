package com.linker.omos.client.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年11月25日
 * @version: 1.0
 * @description: TODO
 */
@AllArgsConstructor
@Getter
public enum WorkflowTaskTypeEnum {

    V1("embeddings", "向量化多模态算法调用", 9, true),
    V1_Text("embeddings-text", "向量化文本算法调用", 8, true),
    RE_RANK("rerank", "重排序算法", 13, true),
    V2("attrdet", "V2-01带属性的目标识别", 99, true),
    OD("od", "目标检测API调用", 10, true),
    OCR("ocr", "ocrAPI调用", 11, true),
    COMPARE("comparison", "比对算法API调用", 12, true),
    V2_02("ovd", "V2-02带label的目标识别v3", 3, true),
    THROUGH("common", "cpu出参入参数透传算法", 88, true),
    WORKER("custom", "编排自定义节点", 77, false),
    ALGORITHM("algorithm", "om算法类型", 1000, true),
    LLM("llm", "omChat 大语言模型API调用兼容openAi", 43, true),
    V3_CHAT("chat-om", "omChat 多模态模型API调用兼容openAi支持流式", 44, true);

    private String code;
    private String desc;
    private Integer codeInteger;
    private Boolean osControl;


    public static WorkflowTaskTypeEnum matchApiType(String apiType) {
        for (WorkflowTaskTypeEnum value : WorkflowTaskTypeEnum.values()) {
            if (value.getCode().equals(apiType)) {
                return value;
            }
        }
        throw new RuntimeException("暂不支持该类型的任务"+ apiType);
    }

    /**
     * os 控制的算法类型
     *
     * @return
     */
    public static List<String> getOsControlCodes() {
        ArrayList<String> codes = new ArrayList<>();

        for (WorkflowTaskTypeEnum value : WorkflowTaskTypeEnum.values()) {
            if (Boolean.TRUE.equals(value.getOsControl())) {
                codes.add(value.getCode());
            }
        }
        return codes;
    }

    /**
     * os 不控制的算法类型
     *
     * @return
     */
    public static List<String> getOsUnControlCodes() {
        ArrayList<String> codes = new ArrayList<>();

        for (WorkflowTaskTypeEnum value : WorkflowTaskTypeEnum.values()) {
            if (!Boolean.TRUE.equals(value.getOsControl())) {
                codes.add(value.getCode());
            }
        }
        return codes;
    }

    public static WorkflowTaskTypeEnum matchStrategyType(String strategyType) {
        for (WorkflowTaskTypeEnum value : WorkflowTaskTypeEnum.values()) {
            if (value.getCode().equals(strategyType)) {
                return value;
            }
        }
        throw new RuntimeException("暂不支持该类型的任务调度:" + strategyType);
    }
}
