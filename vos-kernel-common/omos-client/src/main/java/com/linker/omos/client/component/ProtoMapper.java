package com.linker.omos.client.component;

import com.linker.omos.client.config.WorkflowTaskExecLog;
import com.linker.omos.client.domain.request.v2workflow.TaskMessage;
import com.vos.kernel.common.grpc.GrpcTaskMessage;
import com.vos.kernel.common.grpc.TaskExecLogs;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年05月19日
 * @version: 1.0
 * @description: TODO
 */
public class ProtoMapper {
    public static final ProtoMapper INSTANCE = new ProtoMapper();


    /**
     * 拉取消息格式转换
     *
     * @param protoList
     * @return
     */
    public List<TaskMessage> fromProto(List<GrpcTaskMessage> protoList) {
        List<TaskMessage> taskMessages = new ArrayList<>();
        for (GrpcTaskMessage proto : protoList) {
            TaskMessage taskMessage = new TaskMessage();
            taskMessage.setId(proto.getTaskId());
            taskMessage.setBizMeta(proto.getTaskBizMeta());
            taskMessage.setPayload(proto.getTaskPayload());
            taskMessage.setCallbackUrl(proto.getCallbackUrl());
            taskMessage.setWaitTime((int) proto.getWaitTime());
        }
        return taskMessages;
    }


    /**
     * 消息格式转换
     *
     * @param logs
     * @return
     */
    public List<TaskExecLogs> toProto(List<WorkflowTaskExecLog> logs) {
        List<TaskExecLogs> taskExecLogs = new ArrayList<>();
        for (WorkflowTaskExecLog log : logs) {
            TaskExecLogs taskExecLog = TaskExecLogs.newBuilder()
                    .setLog(log.getLog())
                    .setCreatedTime(log.getCreatedTime()).build();
            taskExecLogs.add(taskExecLog);
        }
        return taskExecLogs;
    }
}
