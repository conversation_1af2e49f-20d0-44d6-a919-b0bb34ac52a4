package com.linker.omos.client.domain.chat;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024年05月06日
 * @version: 1.0
 * @description: TODO
 */
@AllArgsConstructor
@Builder
@Data
@NoArgsConstructor
public class SystemMessage extends ChatMessage implements Serializable {

    /**
     * 内容
     */
    private String content;


    /**
     * 角色
     */
    private String role = ChatMessageType.SYSTEM.getCode();


}
