package com.linker.omos.client.domain.response.cluster;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024年11月14日
 * @version: 1.0
 * @description: TODO
 */
@Data
public class ClusterGpuTokenResponse implements Serializable {

    /**
     * 集群名称
     */
    private String clusterName;

    /**
     * 集群id
     */
    private Long clusterId;

    /**
     * 集群业务id
     */
    private String clusterBizId;

    /**
     * os
     */
    private String machineGroup;

    /**
     * 集群gpu总数
     */
    private Long clusterGpuTotal;

    /**
     * 集群已使用gpu
     */
    private Long clusterTokenGpu;

    /**
     * 集群安装算法数量
     */
    private Integer abilityNum;
}
