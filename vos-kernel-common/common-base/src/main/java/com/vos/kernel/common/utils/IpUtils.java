package com.vos.kernel.common.utils;


import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Enumeration;

@Slf4j
public class IpUtils {

    public static String getLocalIp4Address() {
        try {
            Enumeration<NetworkInterface> allNetInterfaces = NetworkInterface.getNetworkInterfaces();
            while (allNetInterfaces.hasMoreElements()) {
                NetworkInterface netInterface = (NetworkInterface) allNetInterfaces.nextElement();
                Enumeration<InetAddress> addresses = netInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress ip = (InetAddress) addresses.nextElement();
                    if (ip != null
                            && ip instanceof Inet4Address
                            && !ip.isLoopbackAddress() //loopback地址即本机地址，IPv4的loopback范围是********* ~ ***************
                            && ip.getHostAddress().indexOf(":") == -1) {
                        log.info("本机的IP = {}", ip.getHostAddress());
                        return ip.getHostAddress();
                    }
                }
            }
        } catch (Exception e) {
            log.info("获取本机的IP失败", e);
        }
        return null;
    }


    /**
     * 获取主机pod名称作为雪花算法的worderId
     *
     * @return
     */
    public static long getWorkerId() {
        String agentId = StrUtil.isBlank(System.getenv("HOSTNAME")) ? StrUtil.uuid() : System.getenv("HOSTNAME");
        return (agentId.hashCode() & '\uffff');
    }
}
