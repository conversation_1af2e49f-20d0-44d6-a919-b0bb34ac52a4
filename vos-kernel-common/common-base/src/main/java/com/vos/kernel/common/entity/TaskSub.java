package com.vos.kernel.common.entity;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.filter.SimplePropertyPreFilter;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.base.Preconditions;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务-子任务表
 *
 * <AUTHOR>
 * @since 2021-07-19 14:47:57
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("lh_task_sub")
public class TaskSub extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 子任务id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableId(type = IdType.NONE)
    private Long subTaskId;

    /**
     * 业务方id
     */
    private Long appId;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 业务方唯一业务id
     */
    private String appSourceId;


    /**
     * 业务方视频流唯一标识
     */
    private String videoId;

    /**
     * 任务编号
     */
    private String taskNum;

    /**
     * 执行排序 0慢队列 1快队列
     */
    private Integer taskOrder;

    /**
     * 队列选择：ratio优先队列用 废弃
     */
    private Integer taskLevel;

    /**
     * 设备集群来源：用于跨机房任务调度时就近调用
     */
    private String videoClusterSource = "default";

    /**
     * 能力ID
     */
    private Long abilityId;

    /**
     * omAppId
     */
    @TableField(exist = false)
    private String omAppId;

    /**
     * 是否插队 0否 1是
     */
    private Integer isJump;

    /**
     * 插队的时间
     */
    private Date jumpTime;

    /**
     * 单个子任务的请求参数
     */
    private String requestJson;

    /**
     * 请求结果
     */
    private String requestResult;

    /**
     * 业务id
     */
    private String businessId;

    /**
     * 业务方ID
     */
    private String subBusinessId;

    /**
     * 父级子任务ID
     */
    private String parentBusinessIds;

    /**
     * 业务信息（url等）
     */
    private String businessInfo;

    /**
     * 处理状态  0：待处理 10：用户已撤销20：已完成30：执行出错
     */
    private Integer handleStatus;

    /**
     * 处理开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date handleStartTime;

    /**
     * 处理完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date handleFinishTime;

    /**
     * 处理时间（单位：ms）
     */
    private Long handleTime;


    /**
     * 是否是起始节点 0否 1是
     */
    private Integer isFirst;

    /**
     * 是否是结束节点 0否 1是
     */
    private Integer isLast;

    /**
     * 组合任务唯一ID
     */
    private String subSnowId;

    /**
     * 上层业务能力ID
     */
    private Long hzAbilityId;

    /**
     * 扩展参数
     */
    private String extend;

    /**
     * 租户ID
     */
    private Long tenantId;


    /**
     * 请求地址
     */
    private String requestUrl;

    /**
     * 子任务组状态
     */
    private Integer subSnowStatus;

    /**
     * 回流id
     */
    @TableField(exist = false)
    private Long refluxId;


    /**
     * 排队时间
     */
    @TableField(exist = false)
    private String lineTime;

    /**
     * 有效时长
     */
    @TableField(exist = false)
    private Integer waiteTime;

    /**
     * 是否为合并任务
     */
    @TableField(exist = false)
    private Boolean batchRequest = false;

    /**
     * 合并任务图片id对应的设备ID
     */
    @TableField(exist = false)
    private HashMap<String, String> batchImageIdToVideoId;

    /**
     * 合并起来的任务
     */
    @TableField(exist = false)
    private List<TaskSub> batchMergedTaskSub;

    /**
     * 算法对应的请求地址ID
     */
    @TableField(exist = false)
    private String taskServerRouterId;

    /**
     * 依赖的底层预装算法
     */
    @TableField(exist = false)
    private String dependPreset;

    /**
     * 来源：api|task
     */
    @TableField(exist = false)
    private String resource;

    /**
     * 是否跳过
     */
    @TableField(exist = false)
    private Boolean shouldSkip;

    /**
     * 是否为单任务
     */
    @TableField(exist = false)
    private int single;

    /**
     * 主任务副本
     * 任务类型 0：普通任务 1汇目云任务
     */
    @TableField(exist = false)
    private int taskType;

    /**
     * 是否获取原始数据：kernel内部rpc调用 false 组装最终数据； vnet http调用 true
     */
    @TableField(exist = false)
    private Boolean resourceResult;


    /**
     * 单任务&无需持久化存储->直接mq处理
     */
    @TableField(exist = false)
    private Boolean handleDirectByMq;

    /**
     * 单任务独有
     * 回调类型
     */
    @TableField(exist = false)
    private Integer callbackType;

    /**
     * 单任务独有
     * 回调信息
     */
    @TableField(exist = false)
    private String callbackInfo;

    /**
     * 单任务独有
     * 回调信息
     */
    @TableField(exist = false)
    private String metaInfo;

    /**
     * 编码放进队列
     *
     * @param topic
     * @return
     */
    public String encode(String topic) {
        this.lineTime = String.valueOf(System.currentTimeMillis());
        SimplePropertyPreFilter excludeFilter = new SimplePropertyPreFilter();
        excludeFilter.getIncludes().add("tenantId");
        excludeFilter.getIncludes().add("single");
        excludeFilter.getIncludes().add("appId");
        excludeFilter.getIncludes().add("abilityId");
        excludeFilter.getIncludes().add("videoId");
        excludeFilter.getIncludes().add("lineTime");
        excludeFilter.getIncludes().add("subTaskId");
        excludeFilter.getIncludes().add("taskId");
        excludeFilter.getIncludes().add("callbackInfo");
        excludeFilter.getIncludes().add("callbackType");
        excludeFilter.getIncludes().add("waiteTime");
        excludeFilter.getIncludes().add("metaInfo");
        return JSON.toJSONString(this, excludeFilter);
    }

    /**
     * 携带请求消息的格式
     *
     * @param topic
     * @return
     */
    public String encodeWithRequest(String topic) {
        this.lineTime = String.valueOf(System.currentTimeMillis());
        return JSON.toJSONString(this);
    }
}
