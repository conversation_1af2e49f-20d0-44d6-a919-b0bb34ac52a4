package com.vos.kernel.common.utils;

public class EnterpriseCodeUtils {

    public static String getEnterpriseCode(String appKey,String deviceCode){
       String des = MD5Util.string2MD5(appKey+"-"+deviceCode).toUpperCase(); //DESUtils.DESEncode(appKey+"-"+deviceCode, "des");
       String EnterpriseCode = getSplitString(des);
       return EnterpriseCode;
    }


    private static String getSplitString(String str) {
        int len = str.length();
        StringBuilder temp = new StringBuilder();
        for (int i = 0; i < len; i++) {
            if (i % 4 == 0 && i > 0) {
                temp.append("-");
            }
            temp.append(str.charAt(i));
        }
        String[] attrs = temp.toString().split("-");
        StringBuilder finalMachineCode = new StringBuilder();
        for (String attr : attrs) {
            if (attr.length() == 4) {
                finalMachineCode.append(attr).append("-");
            }
        }
        return finalMachineCode.substring(0, finalMachineCode.toString().length() - 1);
    }

}
