package com.vos.kernel.common.property;

import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/10
 * @description: com.vos.kernel.common.property
 */
@Data
@Component
@ConditionalOnMissingBean(LinkerStorageProperties.class)
@ConfigurationProperties(prefix = "linker-storage")
public class LinkerStorageProperties {

    /**
     * 默认存储平台
     */
    private String defaultPlatform = "minio";

    /**
     * ai默认使用对外域名
     */
    private Boolean aiUseOutUrl = true;

    /**
     * gui默认使用对外域名
     */
    private Boolean guiUseOutUrl = true;

    /**
     * 阿里云 OSS
     */
    private List<AliOss> aliOss = new ArrayList<>();

    /**
     * MinIO USS
     */
    private List<MinIO> minio = new ArrayList<>();


    /**
     * WebDAV
     */
    private List<WebDAV> WebDav = new ArrayList<>();


    /**
     * 阿里云 OSS
     */
    @Data
    public static class AliOss {
        private String accessKey;
        private String secretKey;
        /**
         * 请求交互地址
         */
        private String endPoint;
        /**
         * bucketName
         */
        private String bucketName;
        /**
         * 访问域名
         */
        private String domain = "";

        /**
         * 启用存储
         */
        private Boolean enableStorage = false;
        /**
         * 存储平台
         */
        private String platform = "";
        /**
         * 基础路径
         */
        private String basePath = "";
    }


    /**
     * MinIO
     */
    @Data
    public static class MinIO {
        private String accessKey;
        private String secretKey;
        /**
         * api交互地址
         */
        private String endPoint;

        private String bucketName;

        /**
         * 内部访问域名
         */
        private String domain = "";

        /**
         * 外部访问域名
         */
        private String domainOut = "";

        /**
         * 启用存储
         */
        private Boolean enableStorage = false;
        /**
         * 存储平台
         */
        private String platform = "";
        /**
         * 基础路径
         */
        private String basePath = "";
        private String regionName = "china-south-1";
    }


    /**
     * WebDAV
     */
    @Data
    public static class WebDAV {
        /**
         * api服务器地址，注意“/”结尾
         */
        private String server;
        /**
         * 用户名
         */
        private String user;
        /**
         * 密码
         */
        private String password;
        /**
         * 内部访问域名
         */
        private String domain = "";

        /**
         * 外部访问域名
         */
        private String domainOut = "";

        /**
         * 启用存储
         */
        private Boolean enableStorage = false;
        /**
         * 存储平台
         */
        private String platform = "";
        /**
         * 基础路径
         */
        private String basePath = "";
        /**
         * 存储路径，上传的文件都会存储在这个路径下面，默认“/”，注意“/”结尾
         */
        private String storagePath = "/";
    }
}
