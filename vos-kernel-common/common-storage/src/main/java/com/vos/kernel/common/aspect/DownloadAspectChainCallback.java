package com.vos.kernel.common.aspect;

import com.vos.kernel.common.platform.FileStorage;

import java.io.InputStream;
import java.util.function.Consumer;

/**
 * 下载切面调用链结束回调
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/10
 * @description: com.vos.kernel.common.aspect
 */
public interface DownloadAspectChainCallback {

    /**
     * 下载切面调用链结束执行
     *
     * @param relativePath
     * @param fileStorage
     * @param consumer
     */
    void run(String relativePath, FileStorage fileStorage, Consumer<InputStream> consumer);
}
