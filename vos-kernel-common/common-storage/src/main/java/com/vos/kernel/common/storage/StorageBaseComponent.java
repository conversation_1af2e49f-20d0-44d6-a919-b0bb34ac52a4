package com.vos.kernel.common.storage;

import com.vos.kernel.common.enums.WebDavExportEnum;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/12
 * @description: com.vos.kernel.common.storage
 */
@Slf4j
public class StorageBaseComponent {

    /**
     * oss配置
     */
    private OssCommonConfigProperties ossConfig;

    /**
     * webDav配置
     */
    private WebDavCommonConfigProperties webDavConfig;

    public StorageBaseComponent(OssCommonConfigProperties ossConfig, WebDavCommonConfigProperties webDavConfig) {
        this.ossConfig = ossConfig;
        this.webDavConfig = webDavConfig;
    }

    /***
     * 全路径自动拼接
     * ***/
    public String httpFullPath(String path) {
        if (path.startsWith("http") || path.startsWith("https")) {
            return path;
        }
        if (path.startsWith("/image")) {
            path = path.replace("/image", "");
        }
        return webDavConfig.getBase().concat(path);
    }


    /***
     * 截取路径
     * ***/
    public String httpCutPath(String path, String outUrl) {
        if (path.startsWith(outUrl)) {
            return path;
        }
        return outUrl.replace(outUrl, "");
    }


    /**
     * create time: 2022/12/12
     * 路径代理，根据 WebDavExportEnum转换,内转外
     *
     * @return
     ***/
    public String wabDavProxy(String url, Integer export) {
        url = httpFullPath(url);
        if (export.equals(WebDavExportEnum.AI.getCode())) {
            return url.replace(webDavConfig.getBase(), webDavConfig.getAiOutUrl());
        }
        if (export.equals(WebDavExportEnum.GUI.getCode())) {
            return url.replace(webDavConfig.getBase(), webDavConfig.getGuiOutUrl());
        }
        return url;
    }


    /**
     * create time: 2022/12/12
     * 路径代理，根据 WebDavExportEnum转换,外部转内
     *
     * @return
     ***/
    public String wabDavProxyReverse(String url, Integer export) {
        url = httpFullPath(url);
        if (export.equals(WebDavExportEnum.AI.getCode())) {
            return url.replace(webDavConfig.getAiOutUrl(), webDavConfig.getBase());
        }
        if (export.equals(WebDavExportEnum.GUI.getCode())) {
            return url.replace(webDavConfig.getGuiOutUrl(), webDavConfig.getBase());
        }
        return url;
    }
}
