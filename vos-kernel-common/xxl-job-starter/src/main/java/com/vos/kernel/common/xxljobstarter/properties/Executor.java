package com.vos.kernel.common.xxljobstarter.properties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 执行器配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Executor implements Serializable {

    /**
     * 执行器"AppName"和地址信息配置：AppName执行器心跳注册分组依据；地址信息用于"调度中心请求并触发任务"和"执行器注册"。
     */
    private String appName;

    /**
     * 执行器地址
     */
    private String address;

    /**
     * 执行器IP默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用。
     */
    private String ip;

    /**
     * 执行器默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口
     */
    private Integer port;

    /**
     * 执行器运行日志文件存储的磁盘位置，需要对该路径拥有读写权限
     */
    private String logPath;

    /**
     * 执行器Log文件定期清理功能，指定日志保存天数，日志文件过期自动删除。限制至少保持3天，否则功能不生效；
     */
    private Integer logRetentionDays;

    /**
     * 超时时间
     */
    private Integer executeTimeout;

}
