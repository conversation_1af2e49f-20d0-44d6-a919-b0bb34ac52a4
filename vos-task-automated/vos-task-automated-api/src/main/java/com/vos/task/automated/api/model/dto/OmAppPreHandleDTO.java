package com.vos.task.automated.api.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/22
 * @description: com.vos.task.automated.api.model.dto
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OmAppPreHandleDTO implements Serializable {

    /**
     * om appId
     */
    @NotBlank(message = "OM 应用ID 不能为空")
    private String omAppId;

    /**
     * 操作类型：是否关闭
     */
    @NotNull(message = "是否为关闭操作；否则开启")
    private Boolean closeType;


    @NotNull(message = "appKey")
    private String appKey;

    /**
     * 回调地址
     */
    private String callBackUrl;

}
