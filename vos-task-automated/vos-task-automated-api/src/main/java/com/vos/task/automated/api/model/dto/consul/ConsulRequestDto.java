package com.vos.task.automated.api.model.dto.consul;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: huyuxiang
 * @Date: 2022/8/5
 * @ClassName: ConsulRequestDto
 * @Description: com.vos.task.automated.api.model.dto.consul ConsulRequestDto
 */

@Data
@NoArgsConstructor
public class ConsulRequestDto {


    /**
     * 负载权重，默认为1
     */
    private Integer weight = 1;


    /**
     * 最大失败请求次数，默认为2
     */
    private Integer max_fails = 2;

    /**
     * 请求失败超时，默认10
     */
    private Integer fail_timeout = 10;


    /**
     * 是否开启，默认开启
     */
    private Integer down = 0;




}
