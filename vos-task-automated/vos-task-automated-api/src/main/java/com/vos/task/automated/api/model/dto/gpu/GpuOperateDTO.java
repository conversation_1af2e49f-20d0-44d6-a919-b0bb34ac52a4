package com.vos.task.automated.api.model.dto.gpu;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年06月04日
 * @version: 1.0
 * @description: TODO
 */
@Data
public class GpuOperateDTO implements Serializable {

    /**
     * 占用或释放 1占用；0 释放
     */
    private Integer takenOrRelease;

    /**
     * 操作数据
     */
    private List<GpuOperateItemDTO> operateItemList;
}
