apiVersion: apps/v1
kind: Deployment
metadata:
  name: v3-label-extractor
  namespace: ai
  labels:
    app: v3-label-extractor
spec:
  replicas: 1
  selector:
    matchLabels:
      app: v3-label-extractor
  template:
    metadata:
      labels:
        app: v3-label-extractor
    spec:
      nodeSelector:
        app-atom: v3
      containers:
        - name: v3-label-extractor
          image: hbt.linker.cc/omai/018_label_extractor:v1.0.0_encrypted
          imagePullPolicy: IfNotPresent
          env:
            - name: BATCH_SIZE
              value: "10"
          ports:
            - containerPort: 8000
          resources:
            limits:
              nvidia.com/gpu: 1 # 请求2个vGPUs
              nvidia.com/gpumem: 2000 # 每个vGPU申请3000m显存 （可选，整数类型）