<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vos.task.automated.service.mapper.ClusterNodeDrainMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.vos.task.automated.service.entity.ClusterNodeDrainEntity">
        <id column="id" property="id" />
        <result column="cluster_id" property="clusterId" />
        <result column="drain_host" property="drainHost" />
        <result column="drain_info" property="drainInfo" />
        <result column="drain_status" property="drainStatus" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, cluster_id, drain_host, drain_info, drain_status, create_time, update_time
    </sql>

</mapper>
