<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vos.task.automated.service.mapper.TaskQueueInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.vos.task.automated.service.entity.TaskQueueInfoEntity">
        <id column="info_id" property="infoId" />
        <result column="app_id" property="appId" />
        <result column="ability_id" property="abilityId" />
        <result column="topic_name" property="topicName" />
        <result column="producer_group" property="producerGroup" />
        <result column="consumer_group" property="consumerGroup" />
        <result column="task_order" property="taskOrder" />
        <result column="redis_queue_name" property="redisQueueName" />
        <result column="poll_thread_name" property="pollThreadName" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
        <result column="is_deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        info_id, app_id, ability_id, topic_name, producer_group, consumer_group, task_order, redis_queue_name, poll_thread_name, create_time, create_by, update_time, update_by, is_deleted
    </sql>

</mapper>
