package com.vos.task.automated.service.service.impl.ai.install;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.linker.omos.client.domain.CommonConstant;
import com.vos.kernel.common.enums.InstallActionEnum;
import com.vos.kernel.common.enums.PresetAbilityYamlEnum;
import com.vos.kernel.common.install.OmInstallWorkflowTask;
import com.vos.task.automated.api.exception.AiAutomatedException;
import com.vos.task.automated.api.model.dto.AbilityInfo;
import com.vos.task.automated.api.model.dto.DeployInfo;
import com.vos.task.automated.api.model.dto.LicenseDataDto;
import com.vos.task.automated.api.model.dto.OmInfo;
import com.vos.task.automated.api.model.dto.gpu.GpuOperateItemDTO;
import com.vos.task.automated.api.model.dto.k8s.PhysicalGpuDTO;
import com.vos.task.automated.api.model.entity.*;
import com.vos.task.automated.api.model.enums.AbilityApiTypeEnum;
import com.vos.task.automated.service.common.AbilityTypeEnum;
import com.vos.task.automated.service.common.Constants;
import com.vos.task.automated.service.common.ServingTypeEnum;
import com.vos.task.automated.service.config.DeployConfigProperties;
import com.vos.task.automated.service.convert.AbilityConvertMapper;
import com.vos.task.automated.service.entity.AbilityEntity;
import com.vos.task.automated.service.service.*;
import com.vos.task.automated.service.service.batisplus.IAbilityNodeInfoService;
import com.vos.task.automated.service.service.batisplus.IAbilityService;
import com.vos.task.automated.service.service.batisplus.IHydraModelInfoService;
import com.vos.task.automated.service.service.batisplus.IPodRouterInfoService;
import com.vos.task.automated.service.service.impl.ai.InstallPipelineContext;
import com.vos.task.manage.api.model.dto.TaskAbilityServerDTO;
import com.vos.task.manage.api.model.entity.TaskAbility;
import com.vos.task.manage.api.model.entity.TaskApp;
import com.vos.task.manage.api.rpc.TaskAbilityRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 数据库操作相关
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/4
 * @description: com.vos.task.automated.service.service.impl.ai.install
 */
@Slf4j
@Service
public class DataBaseOperate implements ContextHandler {


    @Resource
    ExceptionHandler exceptionHandler;
    @Resource
    DeployConfigProperties deployConfigProperties;

    @Resource
    IAbilityService abilityService;

    @Resource
    PlatformTransactionManager transactionManager;

    @Resource
    NodeGpuService nodeGpuService;

    @Resource
    AtomGpuService atomGpuService;

    @Resource
    NodeGpuLogService nodeGpuLogService;

    @Resource
    AtomService atomService;

    @Resource
    ModelService modelService;

    @Resource
    ServingService servingService;

    @Resource
    LicenseService licenseService;

    @Resource
    AppOperationService appOperationService;

    @Resource
    AtomServingService atomServingService;

    @DubboReference
    TaskAbilityRpcService taskAbilityRpcService;

    @Resource
    ITaskServerRouterService taskServerRouterService;

    @Resource
    IPodRouterInfoService podRouterInfoService;

    @Resource
    IHydraModelInfoService hydraModelInfoService;

    @Resource
    IAbilityNodeInfoService abilityNodeInfoService;

    @Resource
    StringRedisTemplate stringRedisTemplate;

    @Resource
    AbilityConvertMapper abilityConvertMapper;

    /**
     * 是否开启模型文件校验
     */
    @Value("${deploy.abilitySpeed:0.8}")
    private String abilitySpeed;

    @Override
    public boolean handle(InstallPipelineContext context) {
        //编程式事务管理
        DefaultTransactionDefinition defaultTransactionDefinition = new DefaultTransactionDefinition();
        defaultTransactionDefinition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        TransactionStatus transactionStatus = transactionManager.getTransaction(defaultTransactionDefinition);

        OmInstallWorkflowTask workflowTask = context.getOmInstallWorkflow().buildSimpleTask("数据落库", "saveAbility", MapUtil.empty());
        try {
            //能力初始化
            Long abilityId = abilityInit(context);
            log.info("newHZ返回abilityId：「{}」", abilityId);
            //能力安装
            abilityInstall(abilityId, context);
            workflowTask.success(MapUtil.of("abilityId", abilityId));
            transactionManager.commit(transactionStatus);
            return true;
        } catch (Exception e) {
            log.info("k8s 部署发生异常", e);
            workflowTask.error(e.getMessage());
            context.setExceptionMessage(e.getMessage());
            exceptionHandler.cleanOmPackage(context);
            exceptionHandler.cleanK8sResources(context);
            transactionManager.rollback(transactionStatus);
        } finally {
            if (BooleanUtil.isTrue(context.getHydraModelInstalled()) && StrUtil.isNotBlank(context.getModelId())) {
                String key = Constants.HYDRA_MODEL_KEY + context.getModelId();
                stringRedisTemplate.delete(key);
            }
        }
        return false;
    }


    /**
     * 构造实体
     *
     * @param physicalGpuDTO
     * @param deployPattern
     * @param abilityId
     * @return
     */
    private AbilityNodeInfoEntity buildAbilityNodeInfoEntity(PhysicalGpuDTO physicalGpuDTO, Integer deployPattern, Long abilityId, String atomId) {
        AbilityNodeInfoEntity abilityNodeInfoEntity = new AbilityNodeInfoEntity();
        abilityNodeInfoEntity.setAtomId(atomId);
        abilityNodeInfoEntity.setMachineGroup(StrUtil.isBlank(physicalGpuDTO.getMachineGroup()) ? "default" : physicalGpuDTO.getMachineGroup());
        //物理gpu维度
        abilityNodeInfoEntity.setGpuId(physicalGpuDTO.getPhysicalGpuId() == null ? physicalGpuDTO.getGpuId() : physicalGpuDTO.getPhysicalGpuId());
        abilityNodeInfoEntity.setHost(physicalGpuDTO.getHost());
        abilityNodeInfoEntity.setDeployPattern(deployPattern);
        abilityNodeInfoEntity.setStatus(2);
        abilityNodeInfoEntity.setExceptDuplicateNum(1);
        abilityNodeInfoEntity.setAbilityId(abilityId.toString());
        return abilityNodeInfoEntity;
    }


    /**
     * 构造占用数据
     *
     * @param atomInfos
     * @return
     */
    private List<GpuOperateItemDTO> getListGpuOperateItem(List<AtomInfo> atomInfos) {
        List<GpuOperateItemDTO> gpuOperateItems = new ArrayList<>();
        for (AtomInfo a : atomInfos) {
            PhysicalGpuDTO physicalGpu = a.getPhysicalGpuDTO();
            if (physicalGpu != null) {
                if (physicalGpu.getGpuId().contains(",")) {
                    List<String> split = StrUtil.split(physicalGpu.getGpuId(), ",");
                    for (String s : split) {
                        gpuOperateItems.add(new GpuOperateItemDTO(physicalGpu.getHost(), s, a.getK8sName(), Integer.parseInt(a.getResourceSituation())));
                    }
                } else {
                    gpuOperateItems.add(new GpuOperateItemDTO(physicalGpu.getHost(), physicalGpu.getGpuId(), a.getK8sName(), Integer.parseInt(a.getResourceSituation())));
                }
            }
        }
        return gpuOperateItems;
    }


    /**
     * 能力最终确认
     *
     * @param abilityId
     * @param context
     */
    private void abilityInstall(Long abilityId, InstallPipelineContext context) {

        // 更新主机显存信息
        if (CollectionUtil.isNotEmpty(context.getNodeGpuEntityList())) {
            nodeGpuService.saveOrUpdateBatch(context.getNodeGpuEntityList());
        }

        // 保存显存操作日志
        if (CollectionUtil.isNotEmpty(context.getNodeGpuLogEntityList())) {
            nodeGpuLogService.saveOrUpdateBatch(context.getNodeGpuLogEntityList());
        }

        // 更新Atom信息
        atomService.saveOrUpdateBatch(context.getAtomInfoEntityList());
        if (CollectionUtil.isNotEmpty(context.getAtomGpuEntityList())) {
            //保存atom与gpu占用关系
            atomGpuService.saveBatch(context.getAtomGpuEntityList());
        }
        // 更新model信息
        List<ModelInfo> modelInfos = context.getModelInfoEntityList();
        if (CollectionUtil.isNotEmpty(modelInfos)) {
            List<AtomInfo> atomInfos = context.getAtomInfoEntityList();
            for (ModelInfo m : modelInfos) {
                Optional<AtomInfo> atomInfo = atomInfos.stream().filter(t -> m.getAtomName().equals(t.getAtomName())).findFirst();
                atomInfo.ifPresent(info -> m.setAtomId(info.getAtomId()));
            }
            modelService.saveOrUpdateBatch(modelInfos);
        }

        //如果存在共用atom的情况,共用的atomId需要加入serving；atom与serving的绑定关系
        List<Long> atomSelf = context.getAtomInfoEntityList().stream().map(AtomInfo::getAtomId).collect(Collectors.toList());
        context.getServingBindAtomId().addAll(atomSelf);
        String atomIds = context.getServingBindAtomId().stream().map(String::valueOf).collect(Collectors.joining(","));
        ServingInfo servingInfo = context.getServingInfo();
        servingInfo.setAtomIds(atomIds);
        servingInfo.setAtomOnly(BooleanUtil.isTrue(context.getAtomOnly()) ? 1 : 0);
        servingInfo.setAbilityId(abilityId);
        servingInfo.setAbilityCode(context.getLicenseData().getOmVersionId());
        servingInfo.setServingType(context.getServingType());
        servingService.save(servingInfo);

        if (CollectionUtil.isNotEmpty(context.getServingBindAtomId())) {
            //serving与atom的绑定关系;卸载时需要判断是否删除atom
            List<AtomServing> atomServings = context.getServingBindAtomId().stream()
                    .map(t -> new AtomServing(t, servingInfo.getServingId())).collect(Collectors.toList());
            atomServingService.saveBatch(atomServings);
        }

        //lh_ability_node_info数据
        List<AbilityNodeInfoEntity> abilityNodeInfoEntities = new ArrayList<>();
        String servingType = context.getServingType();
        String gpus = "";
        List<GpuOperateItemDTO> gpuOperateItems = new ArrayList<>();
        if (ServingTypeEnum.PYTHON.name().equals(servingType) || ServingTypeEnum.JAVA.name().equals(servingType)) {
            if (CollectionUtil.isNotEmpty(context.getAtomInfoEntityList())) {
                gpuOperateItems.addAll(getListGpuOperateItem(context.getAtomInfoEntityList()));
                gpus = context.getAtomInfoEntityList().stream().map(AtomInfo::getPhysicalGpuDTO).map(t -> t.getHost() + ":" + t.getGpuId()).distinct().collect(Collectors.joining(","));
                PhysicalGpuDTO physicalGpuDTO = CollectionUtil.getFirst(context.getAtomInfoEntityList()).getPhysicalGpuDTO();
                if (physicalGpuDTO != null) {
                    AbilityNodeInfoEntity abilityNodeInfoEntity = buildAbilityNodeInfoEntity(physicalGpuDTO, ServingTypeEnum.PYTHON.name().equals(servingType) ? 0 : 2, abilityId, "");
                    abilityNodeInfoEntities.add(abilityNodeInfoEntity);
                }
            }
        } else if (ServingTypeEnum.HYDRA.name().equals(servingType)) {
            if (CollectionUtil.isNotEmpty(context.getAtomInfoEntityList())) {
                List<PhysicalGpuDTO> collect = context.getAtomInfoEntityList().stream().map(AtomInfo::getPhysicalGpuDTO).filter(Objects::nonNull).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(collect)) {
                    gpuOperateItems.addAll(getListGpuOperateItem(context.getAtomInfoEntityList()));
                    gpus = collect.stream().map(t -> t.getHost() + ":" + t.getGpuId()).distinct().collect(Collectors.joining(","));
                }
            } else {
                if (context.getPhysicalGpuDTO() != null) {
                    gpus = context.getPhysicalGpuDTO().getHost();
                }
            }
            //真实部署的atom
            for (AtomInfo a : context.getAtomInfoEntityList()) {
                PhysicalGpuDTO physicalGpuDTO = a.getPhysicalGpuDTO();
                if (physicalGpuDTO != null) {
                    AbilityNodeInfoEntity abilityNodeInfoEntity = buildAbilityNodeInfoEntity(physicalGpuDTO, 2, abilityId, a.getAtomId().toString());
                    abilityNodeInfoEntity.setAtomK8sName(a.getK8sName());
                    abilityNodeInfoEntities.add(abilityNodeInfoEntity);
                }
            }

        }
        if (CollectionUtil.isNotEmpty(abilityNodeInfoEntities)) {
            abilityNodeInfoService.saveBatch(abilityNodeInfoEntities);
        }

        if (context.getTaskServerRouter() != null) {
            TaskServerRouterEntity taskServerRouter = context.getTaskServerRouter();
            taskServerRouter.setServingId(servingInfo.getServingId());
            taskServerRouter.setGpuInfo(gpus);
            taskServerRouter.setGpuInfoDetail(JSON.toJSONString(ServingTypeEnum.HYDRA.name().equals(servingType) && !BooleanUtil.isTrue(context.getAtomOnly()) ? new ArrayList<>() : gpuOperateItems));
            taskServerRouter.setStatus(2);
            taskServerRouter.setIsInstall(ServingTypeEnum.HYDRA.name().equals(servingType) && BooleanUtil.isTrue(context.getAtomOnly()) ? 5 : 1);
            taskServerRouter.setAbilityId(abilityId);
            taskServerRouter.setScaleType(deployConfigProperties.getScaleType());
            taskServerRouter.setScaleMax(deployConfigProperties.getScaleMax());
            LicenseDataDto licenseData = context.getLicenseData();
            taskServerRouter.setAbilityCode(licenseData.getOmVersionId() + "-" + context.getTaskApp().getAppKey());
            taskServerRouter.setAbilityName(licenseData.getAppName());
            taskServerRouter.setAppKey(context.getTaskApp().getAppKey());
            taskServerRouter.setGpuType(context.getChooseGpuType());
            if (CollectionUtil.isNotEmpty(abilityNodeInfoEntities)) {
                taskServerRouter.setAbilityNodeId(CollectionUtil.getFirst(abilityNodeInfoEntities).getId());
            }
            taskServerRouter.setDeployPattern(ServingTypeEnum.PYTHON.name().equals(servingType) ? 0 : 2);

            List<TaskServerRouterEntity> atomTaskServerRouterEntities = new ArrayList<>();
            //九头蛇部署的需要添加多条 route 信息：serving&atom分离;
            if (ServingTypeEnum.HYDRA.name().equals(servingType) && !BooleanUtil.isTrue(context.getAtomOnly())) {
                //部署的serving abilityNodeInfo
                AbilityNodeInfoEntity abilityNodeInfoEntity = buildAbilityNodeInfoEntity(servingInfo.getPhysicalGpuDTO(), 1, abilityId, "");
                abilityNodeInfoService.save(abilityNodeInfoEntity);
                taskServerRouter.setAbilityNodeId(abilityNodeInfoEntity.getId());
                taskServerRouter.setDeployPattern(1);
                if (StrUtil.isNotBlank(taskServerRouter.getSlavePods())) {
                    List<String> pods = JSON.parseArray(taskServerRouter.getSlavePods(), String.class);
                    if (pods.size() > 1) {
                        List<String> sub = CollectionUtil.sub(pods, pods.size() - 1, pods.size());
                        taskServerRouter.setSlavePods(JSON.toJSONString(sub));
                    }
                }

                //atom的 route
                if (CollectionUtil.isNotEmpty(abilityNodeInfoEntities)) {
                    for (AbilityNodeInfoEntity a : abilityNodeInfoEntities) {
                        TaskServerRouterEntity taskServerRouterEntity = abilityConvertMapper.cloneTaskServerRouter(taskServerRouter);
                        taskServerRouterEntity.setId(null);
                        taskServerRouterEntity.setAbilityNodeId(a.getId());
                        taskServerRouterEntity.setDeployPattern(2);
                        taskServerRouterEntity.setIsInstall(4);
                        String atomK8sName = a.getAtomK8sName();
                        List<GpuOperateItemDTO> collect = gpuOperateItems.stream().filter(t -> t.getAbilityDeployName().equals(atomK8sName)).collect(Collectors.toList());
                        taskServerRouterEntity.setGpuInfoDetail(JSON.toJSONString(collect));
                        List<String> pods = new ArrayList<>();
                        pods.add(atomK8sName);
                        taskServerRouterEntity.setSlavePods(JSON.toJSONString(pods));
                        atomTaskServerRouterEntities.add(taskServerRouterEntity);
                    }
                }
            }

            taskServerRouterService.save(taskServerRouter);

            if (CollectionUtil.isNotEmpty(atomTaskServerRouterEntities)) {
                taskServerRouterService.saveBatch(atomTaskServerRouterEntities);
                for (TaskServerRouterEntity atomTaskServerRouterEntity : atomTaskServerRouterEntities) {
                    if (StringUtils.isNotBlank(atomTaskServerRouterEntity.getSlavePods())) {
                        this.savePodRouter(atomTaskServerRouterEntity);
                    }
                }
            }
            //保存部署名称与能力路由映射
            if (StrUtil.isNotBlank(taskServerRouter.getSlavePods())) {
                this.savePodRouter(taskServerRouter);
            }
            //九头蛇模型信息首次保存
            if (context.getHydraModelInfoEntity() != null) {
                HydraModelInfoEntity hydraModelInfoEntity = context.getHydraModelInfoEntity();
                //设置依赖的蛇身算法ID
                hydraModelInfoEntity.setRouterId(taskServerRouter.getAbilityId());
                hydraModelInfoService.save(hydraModelInfoEntity);
            }
        }


        //保存操作日志及授权信息
        saveOperationLog(context);

    }

    /**
     * 保存pod和route的映射，gpuinfo 展示有用
     *
     * @param taskServerRouter
     */
    private void savePodRouter(TaskServerRouterEntity taskServerRouter) {
        List<String> pods = JSON.parseArray(taskServerRouter.getSlavePods(), String.class);
        List<PodRouterInfoEntity> entities = new ArrayList<>();
        for (String s : pods) {
            entities.add(new PodRouterInfoEntity(s, taskServerRouter.getId(), taskServerRouter.getAbilityId(), taskServerRouter.getSlaveAddress(), ""));
        }
        podRouterInfoService.saveBatch(entities);
        podRouterInfoService.clearPodRouterByCluster(taskServerRouter.getSlaveAddress());
    }


    /**
     * 保存操作日志及授权信息
     *
     * @param context
     */
    private void saveOperationLog(InstallPipelineContext context) {
        //保存licenseInfo 记录
        TaskApp taskApp = context.getTaskApp();
        LicenseDataDto licenseData = context.getLicenseData();
        String omAppId = licenseData.getOmVersionId() + "-" + taskApp.getAppKey();
        LicenseInfo licenseInfo = new LicenseInfo();
        licenseInfo.setTenantId(taskApp.getTenantId());
        licenseInfo.setAppId(taskApp.getAppId());
        licenseInfo.setOmAppId(omAppId);
        licenseInfo.setAppVersionId(licenseData.getAppVersionId());
        licenseInfo.setAction(InstallActionEnum.INSTALL.getValue());
        licenseInfo.setDecodedData(JSONObject.toJSONString(licenseData));
        licenseService.save(licenseInfo);
        //初始化租户操作记录
        AppOperationInfo appOperationInfo = new AppOperationInfo();
        appOperationInfo.setTenantId(taskApp.getTenantId());
        appOperationInfo.setAppId(taskApp.getAppId());
        appOperationInfo.setOmAppId(omAppId);
        appOperationInfo.setAppVersionId(licenseData.getAppVersionId());
        appOperationInfo.setAppName(licenseData.getAppName());
        appOperationInfo.setAction(InstallActionEnum.INSTALL.getValue());
        appOperationInfo.setOperationStatus(1);
        appOperationService.save(appOperationInfo);
    }

    /**
     * 初始化能力和权益信息
     *
     * @param context
     * @return
     */
    private Long abilityInit(InstallPipelineContext context) {

        // 调用 newhuizhi 接口初始化能力和权益信息并获取abilityId
        LicenseDataDto licenseData = context.getLicenseData();
        JSONObject atomConfig = context.getOmInstallInfo().getAtomConfig();
        DeployInfo deployInfo = buildDeployInfo(licenseData, context.getTaskApp(), context.getServingInfo(), atomConfig);
        QueryWrapper<AbilityEntity> queryWrapper = new QueryWrapper<>();
        AbilityInfo abilityInfo = deployInfo.getAbilityInfo();
        queryWrapper.lambda().eq(AbilityEntity::getAbilityId, abilityInfo.getAbilityId())
                .eq(AbilityEntity::getDel, 0);
        List<AbilityEntity> list = abilityService.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            throw new AiAutomatedException("500", "能力已存在不可重复安装");
        }
        AbilityEntity abilityEntity = new AbilityEntity();
        OmInfo omInfo = deployInfo.getOmInfo();

        abilityEntity.setFormula("");
        abilityEntity.setAbilityId(abilityInfo.getAbilityId());
        abilityEntity.setAbilityCode(abilityInfo.getAbilityCode());
        abilityEntity.setType(AbilityTypeEnum.SINGLE.getCode());
        abilityEntity.setClassifyId(1L);
        abilityEntity.setMaxOperator(abilityInfo.getMaxOperator());
        abilityEntity.setAbilityName(abilityInfo.getAbilityName());
        abilityEntity.setServerUrl(abilityInfo.getServerUrl());
        abilityEntity.setAbilityRemark(abilityInfo.getAbilityRemark());
        abilityEntity.setReflowUrl(omInfo.getReflowUrl());
        abilityEntity.setVersionId(omInfo.getVersionId());
        abilityEntity.setStatus(true);

//        if (BooleanUtil.isTrue(context.getIsV3Serving())) {
//            abilityEntity.setAbilityEnum(AbilityOmTypeEnum.V3.getCode());
//            abilityEntity.setDependPreset(PresetAbilityYamlEnum.IMAGE_MODEL.getServiceName());
//        }
//        if (BooleanUtil.isTrue(context.getIsVector())) {
//            abilityEntity.setAbilityEnum(AbilityOmTypeEnum.VECTOR.getCode());
//        } else {
//            AbilityOmTypeEnum typeBySuffix = AbilityOmTypeEnum.getTypeBySuffix(abilityInfo.getAbilityCode());
//            abilityEntity.setAbilityEnum(typeBySuffix.getCode());
//        }

        abilityEntity.setAbilityEnum(context.getAbilityApiType() == null ? AbilityApiTypeEnum.V2.getCodeInteger() : context.getAbilityApiType().getCodeInteger());
        //比对算法安装
        if (abilityEntity.getAbilityEnum().equals(AbilityApiTypeEnum.COMPARE.getCodeInteger())) {
            abilityEntity.setIsMatchType(true);
            if (abilityInfo.getServerUrl().contains(CommonConstant.HTTP)) {
                abilityEntity.setMatchUploadUrl(abilityInfo.getServerUrl().replaceAll("serving", "upload"));
            } else {
                TaskServerRouterEntity taskServerRouter = context.getTaskServerRouter();
                if (taskServerRouter != null) {
                    abilityEntity.setMatchUploadUrl(taskServerRouter.getTaskServerUrl().replaceAll("serving", "upload"));
                }
            }
            licenseData.setIsMatchType(true);
            licenseData.setMatchUploadUrl(abilityEntity.getMatchUploadUrl());
        }

        // 调用新汇智初始化能力和权益接口
        TaskAbilityServerDTO taskAbilityServerDTO = new TaskAbilityServerDTO();
        taskAbilityServerDTO.setAppKey(deployInfo.getAppKey());
        taskAbilityServerDTO.setAbilityName(abilityInfo.getAbilityName());
        taskAbilityServerDTO.setAbilityRemark(abilityInfo.getAbilityRemark());
        taskAbilityServerDTO.setArithmeticIds("");
        taskAbilityServerDTO.setAbilityConcurrent(abilityInfo.getMaxOperator());
        taskAbilityServerDTO.setServerInterface(abilityInfo.getServerUrl());
        taskAbilityServerDTO.setVersionId(omInfo.getVersionId());
        taskAbilityServerDTO.setServingType(context.getServingType());
        taskAbilityServerDTO.setAbilityCode(context.getLicenseData().getOmVersionId());
        if (BooleanUtil.isTrue(context.getIsV3Serving())) {
            taskAbilityServerDTO.setPresetAbility(PresetAbilityYamlEnum.IMAGE_MODEL);
        }

        TaskAbility taskAbility = taskAbilityRpcService.addWithServer(taskAbilityServerDTO);
        abilityEntity.setOperatorId(taskAbility.getAbilityId());
        abilityService.save(abilityEntity);
        return taskAbility.getAbilityId();

    }


    /**
     * 构造数据
     *
     * @param licenseData
     * @param taskApp
     * @param servingInfo
     * @return
     */
    private DeployInfo buildDeployInfo(LicenseDataDto licenseData, TaskApp taskApp, ServingInfo servingInfo, JSONObject atomConfig) {
        DeployInfo deployInfo = new DeployInfo();
        OmInfo omInfo = new OmInfo();
        AbilityInfo abilityInfo = new AbilityInfo();
        omInfo.setVersionId(licenseData.getReflowVersionId());
        omInfo.setReflowUrl(licenseData.getReflowUrl());
        abilityInfo.setAbilityCode(licenseData.getOmVersionId());
        abilityInfo.setAbilityId(licenseData.getOmVersionId() + "-" + taskApp.getAppKey());

        abilityInfo.setAbilityName(licenseData.getAppName());
        if (licenseData.getConcurrent() == null || licenseData.getConcurrent() == 0) {
            // 若om包该值为空 则填默认值
            abilityInfo.setMaxOperator(2);
        }
        if (StringUtils.isNotBlank(servingInfo.getServingRequestUrl())) {
            abilityInfo.setServerUrl(servingInfo.getServingRequestUrl());
        } else {
            // ServerUrl为空填默认值
            abilityInfo.setServerUrl("http://ip:port/vql/v1/serving/process");
        }
        deployInfo.setOmInfo(omInfo);
        deployInfo.setAbilityInfo(abilityInfo);
        deployInfo.setAppKey(taskApp.getAppKey());

        return deployInfo;
    }


}
