package com.vos.task.automated.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linker.omos.client.domain.response.cluster.AbilityItemResponse;
import com.linker.omos.client.domain.response.cluster.ClusterScheduleConfigResponse;
import com.vos.task.automated.service.entity.ClusterScaleConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 远程调度配置信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Mapper
public interface ClusterScaleConfigMapper extends BaseMapper<ClusterScaleConfigEntity> {


    /**
     * 获取具体算法集群调度配置
     *
     * @param abilityId
     * @return
     */
    ClusterScheduleConfigResponse getScheduleConfig(@Param("abilityId") Long abilityId);

    /**
     * 获取算法列表和调度配置
     *
     * @param page
     * @param abilityIds
     * @return
     */
    IPage<AbilityItemResponse> listAbilityWithScheduleConfig(@Param("page") Page<AbilityItemResponse> page
            , @Param("abilityIds") List<Long> abilityIds, @Param("abilityCodes") List<String> abilityCodes);

}
