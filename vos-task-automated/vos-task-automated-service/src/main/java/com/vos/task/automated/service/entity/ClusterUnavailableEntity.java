package com.vos.task.automated.service.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 集群不可用状态表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-26
 */
@Getter
@Setter
@TableName("lh_cluster_unavailable")
public class ClusterUnavailableEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 集群 slaveAddress+nodeIp
     */
    @TableField(exist = false)
    private String nodeKey;

    /**
     * 集群
     */
    @TableField("slave_address")
    private String slaveAddress;

    /**
     * 主机
     */
    @TableField("node_ip")
    private String nodeIp;

    /**
     * 显卡ID
     */
    @TableField("gpu_id")
    private String gpuId;

    /**
     * 重试次数
     */
    @TableField("reset_num")
    private Integer resetNum;

    /**
     * 当前状态：异常 1、已恢复2、已迁移3
     */
    @TableField("status")
    private Integer status;

    /**
     * 当前状态：异常类型 node 1 、gpu 2
     */
    @TableField("exception_type")
    private Integer exceptionType;

    /**
     * 最近重试次数
     */
    @TableField("last_reset_time")
    private Date lastResetTime;

    /**
     * 创建日期
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
