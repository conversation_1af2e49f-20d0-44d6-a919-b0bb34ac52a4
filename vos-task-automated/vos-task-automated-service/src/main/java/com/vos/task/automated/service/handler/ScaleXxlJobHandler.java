package com.vos.task.automated.service.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.vos.task.automated.api.model.entity.ScaleHistoryEntity;
import com.vos.task.automated.api.model.entity.TaskServerRouterEntity;
import com.vos.task.automated.api.model.enums.ScalingStatusEnum;
import com.vos.task.automated.service.service.IScaleHistoryService;
import com.vos.task.automated.service.service.impl.ConcurrentScaleService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

import static com.alibaba.nacos.common.utils.ExceptionUtil.getStackTrace;

/**
 * <AUTHOR>
 * @since 2024/6/18
 */
@Component
@Slf4j
public class ScaleXxlJobHandler {

    private final ConcurrentScaleService concurrentScaleService;
    private final IScaleHistoryService scaleHistoryService;

    public ScaleXxlJobHandler(ConcurrentScaleService concurrentScaleService, IScaleHistoryService scaleHistoryService) {
        this.concurrentScaleService = concurrentScaleService;
        this.scaleHistoryService = scaleHistoryService;
    }

    @XxlJob(value = "scaleBaseOnQpsHandler")
    public ReturnT<String> scaleBaseOnQpsHandler() {
        String param = XxlJobHelper.getJobParam();
        if (StrUtil.isBlank(param)) {
            log.warn("定时任务参数未设置");
            return ReturnT.SUCCESS;
        }
        String abilityId = JSONObject.parseObject(param).getString("abilityId");
        Integer requestQps = JSONObject.parseObject(param).getInteger("requestQps");
        Long historyId = JSONObject.parseObject(param).getLong("historyId");
        QueryWrapper<ScaleHistoryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ScaleHistoryEntity::getId, historyId);
        ScaleHistoryEntity scaleHistoryEntity = scaleHistoryService.getById(historyId);
        scaleHistoryEntity.setStatus(ScalingStatusEnum.IN_PROGRESS.getStatus());
        scaleHistoryEntity.setUpdateTime(DateUtil.formatDateTime(new Date()));
        scaleHistoryService.update(scaleHistoryEntity, queryWrapper);
        try {
            concurrentScaleService.scaleBasedOnQPS(abilityId, requestQps, historyId);
            List<TaskServerRouterEntity> copyRouters = concurrentScaleService.getCopyRouters(Long.valueOf(abilityId));
            int currentNodeCount = CollectionUtil.isEmpty(copyRouters) ? 0 : copyRouters.size();
            currentNodeCount = currentNodeCount + 1;
            scaleHistoryEntity.setActualNodeCount(currentNodeCount);
            if (scaleHistoryEntity.getActualNodeCount() < scaleHistoryEntity.getExpectedNodeCount()) {
                scaleHistoryEntity.setStatus(ScalingStatusEnum.FAILED.getStatus());
            } else {
                scaleHistoryEntity.setStatus(ScalingStatusEnum.SUCCESSFUL.getStatus());
            }
            scaleHistoryEntity.setUpdateTime(DateUtil.formatDateTime(new Date()));
            scaleHistoryService.update(scaleHistoryEntity, queryWrapper);
        } catch (Exception e) {
            log.error("abilityId: {}, requestQps: {}, historyId: {} scaleBaseOnQpsHandler error", abilityId, requestQps, historyId, e);
            List<TaskServerRouterEntity> copyRouters = concurrentScaleService.getCopyRouters(Long.valueOf(abilityId));
            int currentNodeCount = CollectionUtil.isEmpty(copyRouters) ? 0 : copyRouters.size();
            currentNodeCount = currentNodeCount + 1;
            scaleHistoryEntity.setActualNodeCount(currentNodeCount);
            scaleHistoryEntity.setStatus(ScalingStatusEnum.FAILED.getStatus());
            scaleHistoryEntity.setUpdateTime(DateUtil.formatDateTime(new Date()));
            scaleHistoryEntity.setDetail(getStackTrace(e));
            scaleHistoryService.update(scaleHistoryEntity, queryWrapper);
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob(value = "scaleToTargetNodeCountHandler")
    public ReturnT<String> scaleToTargetNodeCount() {
        String param = XxlJobHelper.getJobParam();
        if (StrUtil.isBlank(param)) {
            log.warn("定时任务参数未设置");
            return ReturnT.SUCCESS;
        }
        JSONObject jsonObject = JSONObject.parseObject(param);
        String abilityId = jsonObject.getString("abilityId");
        Integer targetNodeCount = jsonObject.getInteger("targetNodeCount");
        Long historyId = jsonObject.getLong("historyId");
        QueryWrapper<ScaleHistoryEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ScaleHistoryEntity::getId, historyId);
        ScaleHistoryEntity scaleHistoryEntity = scaleHistoryService.getById(historyId);
        scaleHistoryEntity.setStatus(ScalingStatusEnum.IN_PROGRESS.getStatus());
        scaleHistoryEntity.setUpdateTime(DateUtil.formatDateTime(new Date()));
        scaleHistoryService.update(scaleHistoryEntity, queryWrapper);
        try {
            concurrentScaleService.scaleToTargetNodeCount(abilityId, targetNodeCount, historyId);
            scaleHistoryEntity.setUpdateTime(DateUtil.formatDateTime(new Date()));
            List<TaskServerRouterEntity> copyRouters = concurrentScaleService.getCopyRouters(Long.valueOf(abilityId));
            int currentNodeCount = CollectionUtil.isEmpty(copyRouters) ? 0 : copyRouters.size();
            currentNodeCount = currentNodeCount + 1;
            scaleHistoryEntity.setActualNodeCount(currentNodeCount);
            if (scaleHistoryEntity.getActualNodeCount() < scaleHistoryEntity.getExpectedNodeCount()) {
                scaleHistoryEntity.setStatus(ScalingStatusEnum.FAILED.getStatus());
            } else {
                scaleHistoryEntity.setStatus(ScalingStatusEnum.SUCCESSFUL.getStatus());
            }
            scaleHistoryService.update(scaleHistoryEntity, queryWrapper);
        } catch (Exception e) {
            log.error("abilityId: {}, targetNodeCount: {}, historyId: {} scaleToTargetNodeCount error", abilityId, targetNodeCount, historyId, e);
            scaleHistoryEntity.setStatus(ScalingStatusEnum.FAILED.getStatus());
            scaleHistoryEntity.setUpdateTime(DateUtil.formatDateTime(new Date()));
            scaleHistoryEntity.setDetail(getStackTrace(e));
            List<TaskServerRouterEntity> copyRouters = concurrentScaleService.getCopyRouters(Long.valueOf(abilityId));
            int currentNodeCount = CollectionUtil.isEmpty(copyRouters) ? 0 : copyRouters.size();
            currentNodeCount = currentNodeCount + 1;
            scaleHistoryEntity.setActualNodeCount(currentNodeCount);
            scaleHistoryService.update(scaleHistoryEntity, queryWrapper);
        }
        return ReturnT.SUCCESS;
    }
}
