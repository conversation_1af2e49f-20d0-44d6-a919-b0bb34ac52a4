package com.vos.task.automated.service.controller.vo.serving.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022-05-19 14:47:57
 */
@Data
@ApiModel("应用atom修改信息")
public class TaskTypeOmAtomReq {

    @ApiModelProperty(value = "atom的id", example = "1")
    private Long atomId;

    @ApiModelProperty(value = "atom算力消耗", example = "100")
    private Integer changeComPower;
}
