package com.vos.task.automated.service.utils.k8s;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024年11月21日
 * @version: 1.0
 * @description: TODO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class K8sPatchDTO implements Serializable {

    /**
     * 操作
     */
    private String op;

    /**
     * 路径
     */
    private String path;

    private Object value;
}
