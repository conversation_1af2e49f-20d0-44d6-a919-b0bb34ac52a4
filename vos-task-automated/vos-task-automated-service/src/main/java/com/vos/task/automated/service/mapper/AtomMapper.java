package com.vos.task.automated.service.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.vos.task.automated.api.model.dto.ServingDuplicateDTO;
import com.vos.task.automated.api.model.entity.AtomInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-04-27 14:47:57
 */
@Mapper
public interface AtomMapper extends BaseMapper<AtomInfo> {


    /**
     * 获取同租户下同名atom 包含模型文件信息
     *
     * @param atomName
     * @param tenantId
     * @return
     */
    List<AtomInfo> querySameNameAtom(@Param("atomName") String atomName, @Param("tenantId") Long tenantId);


    /**
     * 获取执行中的信息
     *
     * @return
     */
    List<ServingDuplicateDTO> abilityFps(@Param("virtual") Integer virtual);

    /**
     * 获取最小处理能力
     *
     * @param atomIds
     * @return
     */
    Integer getMinAtomFps(@Param("atomIds") List<String> atomIds);

}