package com.vos.task.automated.service.service.impl.ai.omhandler;

import com.alibaba.cloud.nacos.NacosConfigManager;
import com.alibaba.nacos.api.exception.NacosException;
import com.google.common.collect.Lists;
import com.vos.task.automated.api.model.dto.AbilityDeployDTO;
import com.vos.task.automated.api.model.dto.OmGpuInfo;
import com.vos.task.automated.api.model.dto.ServingDeployDTO;
import com.vos.task.automated.api.model.dto.om.ServingConfigVo;
import com.vos.task.automated.api.model.entity.AtomInfo;
import com.vos.task.automated.api.model.entity.ServingInfo;
import com.vos.task.automated.service.config.KubernetesConfigProperties;
import com.vos.task.automated.service.service.IAbilityOperateService;
import com.vos.task.automated.service.service.impl.ai.InstallPipelineContext;
import com.vos.task.automated.service.service.impl.ai.gpu.GpuInfoHelper;
import com.vos.task.automated.service.service.impl.ai.install.BaseDeployOperate;
import com.vos.task.automated.service.service.impl.ai.master.RoleProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import static org.mockito.Mockito.*;

class JavaServingOmHandlerTest {
    @Mock
    BaseDeployOperate baseDeployOperate;
    @Mock
    IAbilityOperateService abilityOperateService;
    @Mock
    KubernetesConfigProperties kubernetesConfigProperties;
    @Mock
    GpuInfoHelper gpuInfoHelper;
    @Mock
    NacosConfigManager nacosConfigManager;
    @Mock
    Logger log;
    @Mock
    OmAiHandlerHolder omAiHandlerHolder;
    @Mock
    RoleProperties roleProperties;
    @InjectMocks
    JavaServingOmHandler javaServingOmHandler;
    @InjectMocks
    PythonServingOmHandler pythonServingOmHandler;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testDoHealthCheck() {
//        javaServingOmHandler.doHealthCheck(new InstallPipelineContext(Boolean.TRUE, new OmPreInstallEntity(), Boolean.TRUE, Boolean.TRUE, Boolean.TRUE, Arrays.<String>asList("String"), new TaskServerRouterEntity(), Boolean.TRUE, Boolean.TRUE, new OmAppUploadDto(), new OmAppInstallOnlineDTO(), Boolean.TRUE, new TaskApp(), Boolean.TRUE, Long.valueOf(1), "callBackUrl", LocalDateTime.of(2023, Month.OCTOBER, 11, 10, 22, 59), new OmGpuInfo(), "chooseGpuType", new AbilityDeployDTO(), "omDownloadPath", "omZipPath", new LicenseDataDto(), new OmBaseInfo(), null, Arrays.<CheckResultDTO>asList(new CheckResultDTO()), "evaluateImagePath", Arrays.<String>asList("String"), Arrays.<String>asList("String"), Arrays.<String>asList("String"), Arrays.<NodeGpu>asList(new NodeGpu()), Arrays.<NodeGpuLog>asList(new NodeGpuLog()), Arrays.<AtomGpu>asList(new AtomGpu()), Arrays.<AtomInfo>asList(new AtomInfo()), Arrays.<ModelInfo>asList(new ModelInfo()), Arrays.<Long>asList(Long.valueOf(1)), new ServingInfo(), "exceptionMessage", new Node(), "deploySlaveCode", "deploySlaveAddress", Arrays.<String>asList("String"), "atomConfEnv", new PhysicalGpuDTO(), "servingType", Boolean.TRUE));
    }

    @Test
    void testMasterDeploy() {
        when(baseDeployOperate.buildSingleAtom(any(), anyString(), any())).thenReturn(new AtomInfo());
        when(baseDeployOperate.atomResourceCalculate(anyString(), any(), any(), any())).thenReturn(Integer.valueOf(0));
        when(abilityOperateService.addConfigmap(anyString(), anyString(), anyString())).thenReturn(Boolean.TRUE);
        when(abilityOperateService.addClusterIpService(anyString(), anyInt(), anyString())).thenReturn("addClusterIpServiceResponse");
        when(abilityOperateService.addAtomPodDeploy(any())).thenReturn("addAtomPodDeployResponse");
        when(kubernetesConfigProperties.isVgpuEnable()).thenReturn(true);

        AbilityDeployDTO abilityDeployDTO = new AbilityDeployDTO();
        ServingDeployDTO servingDeployDTO = new ServingDeployDTO();
        ServingInfo servingInfo = new ServingInfo();
        servingInfo.setOmAppId("OD004_000_000010_003_RFjS5fhx");
        servingDeployDTO.setServingDeploy(servingInfo);
        abilityDeployDTO.setServingDeployInfo(servingDeployDTO);
        abilityDeployDTO.setAtomDeployInfo(Lists.newArrayList());
        abilityDeployDTO.setModelUrlList(Lists.newArrayList());
        abilityDeployDTO.setIsScale(false);
        abilityDeployDTO.setOmChooseGpuType("T$");
        abilityDeployDTO.setServingType("JAVA");
        abilityDeployDTO.setOmGpuInfo(new OmGpuInfo());
        InstallPipelineContext installPipelineContext = new InstallPipelineContext();
        installPipelineContext.setAbilityDeployInfo(abilityDeployDTO);
        installPipelineContext.setChooseGpuType(abilityDeployDTO.getOmChooseGpuType());
        installPipelineContext.setOmGpuInfo(abilityDeployDTO.getOmGpuInfo());
        installPipelineContext.setServingType(abilityDeployDTO.getServingType());
        javaServingOmHandler.doDeploy(installPipelineContext);
    }

    @Test
    void testPythonOmDeploy() throws NacosException {
        when(baseDeployOperate.buildSingleAtom(any(), anyString(), any())).thenReturn(new AtomInfo());
        when(baseDeployOperate.atomResourceCalculate(anyString(), any(), any(), any())).thenReturn(Integer.valueOf(0));
        when(abilityOperateService.addConfigmap(anyString(), anyString(), anyString())).thenReturn(Boolean.TRUE);
        when(abilityOperateService.addClusterIpService(anyString(), anyInt(), anyString())).thenReturn("addClusterIpServiceResponse");
        when(abilityOperateService.addAtomPodDeploy(any())).thenReturn("addAtomPodDeployResponse");
        when(kubernetesConfigProperties.isVgpuEnable()).thenReturn(true);

        AbilityDeployDTO abilityDeployDTO = new AbilityDeployDTO();
        ServingDeployDTO servingDeployDTO = new ServingDeployDTO();
        ServingInfo servingInfo = new ServingInfo();
        servingInfo.setOmAppId("OD004_000_000010_003_RFjS5fhx");
        ServingConfigVo servingConfigVo = new ServingConfigVo();
        servingDeployDTO.setServingConfig(servingConfigVo);
        servingDeployDTO.setServingDeploy(servingInfo);
        abilityDeployDTO.setServingDeployInfo(servingDeployDTO);
        abilityDeployDTO.setAtomDeployInfo(Lists.newArrayList());
        abilityDeployDTO.setModelUrlList(Lists.newArrayList());
        abilityDeployDTO.setIsScale(false);
        abilityDeployDTO.setOmChooseGpuType("T$");
        abilityDeployDTO.setServingType("JAVA");
        abilityDeployDTO.setOmGpuInfo(new OmGpuInfo());
        InstallPipelineContext installPipelineContext = new InstallPipelineContext();
        installPipelineContext.setAbilityDeployInfo(abilityDeployDTO);
        installPipelineContext.setChooseGpuType(abilityDeployDTO.getOmChooseGpuType());
        installPipelineContext.setOmGpuInfo(abilityDeployDTO.getOmGpuInfo());
        installPipelineContext.setServingType(abilityDeployDTO.getServingType());
        pythonServingOmHandler.doDeploy(installPipelineContext);
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme