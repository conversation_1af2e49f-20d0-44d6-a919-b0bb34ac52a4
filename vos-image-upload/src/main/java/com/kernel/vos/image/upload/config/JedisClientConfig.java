package com.kernel.vos.image.upload.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/8
 * @description: com.kernel.vos.image.upload.config
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class JedisClientConfig {

    private int maxTotal;
    private int maxIdle;
    private int minIdle;
    private int maxWait;
    private int database;
    /**
     * 在获取连接时是否检查有效性
     */
    public boolean testOnBorrow;

    /**
     * 在归还连接时是否检查有效性
     */
    public boolean testOnReturn;

    public boolean getTestOnBorrow() {
        return this.testOnBorrow;
    }

    public boolean getTestOnReturn() {
        return this.testOnReturn;
    }
}
