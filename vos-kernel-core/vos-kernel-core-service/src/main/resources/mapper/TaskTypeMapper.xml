<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vos.kernel.core.service.mapper.TaskTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.vos.kernel.core.api.domain.TaskTypeEntity">
        <id column="id" property="id" />
        <id column="task_type_code" property="taskTypeCode" />
        <result column="name" property="name" />
        <result column="parentid" property="parentid" />
        <result column="ability" property="ability" />
        <result column="ability_type" property="abilityType" />
        <result column="action_id" property="actionId" />
        <result column="is_check" property="isCheck" />
        <result column="forecast" property="forecast" />
        <result column="msg_water" property="msgWater" />
        <result column="msg_desc" property="msgDesc" />
        <result column="msg_type" property="msgType" />
        <result column="msg_condition" property="msgCondition" />
        <result column="version_id" property="versionId" />
        <result column="reflow_url" property="reflowUrl" />
        <result column="error_msg" property="errorMsg" />
        <result column="action_des" property="actionDes" />
        <result column="status" property="status" />
        <result column="frequency" property="frequency" />
        <result column="orgcode" property="orgcode" />
        <result column="is_draw" property="isDraw" />
        <result column="is_positions" property="isPositions" />
        <result column="is_sequential" property="sequential" />
        <result column="need_camera" property="needCamera" />
        <result column="is_hide" property="isHide" />
        <result column="isdel" property="del" />
        <result column="handlertype" property="handlertype" />
        <result column="data_type" property="dataType" />
        <result column="addtime" property="addtime" />
        <result column="updatetime" property="updatetime" />
        <result column="iscreate" property="iscreate" />
        <result column="needoperation" property="needoperation" />
        <result column="engineId" property="engineId" />
        <result column="contains" property="contains" />
        <result column="app_id" property="appId" />
        <result column="app_version_id" property="appVersionId" />
        <result column="version_num" property="versionNum" />
        <result column="app_description" property="appDescription" />
        <result column="version_alias" property="versionAlias" />
        <result column="version_description" property="versionDescription" />
        <result column="app_cover" property="appCover" />
        <result column="com_power" property="comPower" />
        <result column="concurrent" property="concurrent" />
        <result column="auth_type" property="authType" />
        <result column="custom_name" property="customName" />
        <result column="expire_date" property="expireDate" />
        <result column="device_identity" property="deviceIdentity" />
        <result column="device_limit" property="deviceLimit" />
        <result column="remark" property="remark" />
        <result column="om_org_code" property="omOrgCode" />
        <result column="app_status" property="appStatus" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="server_url" property="serverUrl" />
        <result column="enable" property="enable" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, parentid, ability, ability_type, action_id, is_check, forecast, msg_water, msg_desc, msg_type, msg_condition, version_id, reflow_url, error_msg, action_des, status, frequency, orgcode, is_draw, is_positions, is_sequential, need_camera, is_hide, isdel, handlertype, data_type, addtime, updatetime, iscreate, needoperation, engineId, contains, app_id, app_version_id, version_num, app_description, version_alias, version_description, app_cover, com_power, concurrent, auth_type, custom_name, expire_date, device_identity, device_limit, remark, om_org_code, app_status, create_user, create_time, server_url, enable
    </sql>

</mapper>
