<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vos.kernel.core.service.mapper.TaskResultCountMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.vos.kernel.core.api.domain.TaskResultCountEntity">
        <id column="id" property="id"/>
        <result column="action_id" property="actionId"/>
        <result column="org_code" property="orgCode"/>
        <result column="ymd" property="ymd"/>
        <result column="ai_back_num" property="aiBackNum"/>
        <result column="call_num" property="callNum"/>
        <result column="ai_success_num" property="aiSuccessNum"/>
        <result column="ai_fail_num" property="aiFailNum"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, action_id, org_code, ymd, ai_back_num, call_num, ai_success_num, ai_fail_num
    </sql>

    <select id="getStatisticsByAbilityEnum" resultType="com.vos.kernel.core.service.entity.OmHubStatisticsDTO">
        select ability_enum,
        ymd,
        sum(ai_success_num) as successNum,
        sum(prompt_tokens_num) as promptTokensNum,
        sum(output_tokens_num) as outputTokensNum
        from t_task_result_count
        where ymd = #{date}
        group by ability_enum, ymd;

    </select>


    <select id="getStatisticsByTenant" resultType="com.vos.kernel.core.service.entity.OmHubStatisticsDTO">
        select ymd,
        sum(ai_success_num) as successNum,
        sum(prompt_tokens_num) as promptTokensNum,
        sum(output_tokens_num) as outputTokensNum
        from t_task_result_count
        where ymd <![CDATA[ >= ]]> #{startDate}
        and ymd <![CDATA[ <= ]]> #{endDate}
        and org_code = #{tenantId}
        group by ymd
        order by ymd;
    </select>

    <select id="getUserList" resultType="com.vos.kernel.core.service.entity.UserDTO">
        select authentication_id as orgCode,
        app_key as appKey
        from t_user_authentication
    </select>

    <select id="getAbilityByTaskAbilityId" resultType="com.vos.kernel.core.api.domain.AbilityEntity">
        SELECT ability_code, ability_enum,ability_id
        FROM tb_ability
        where operator_id = #{abilityId} limit 1
    </select>
    <select id="getAbilityList" resultType="com.vos.kernel.core.api.domain.AbilityEntity">
        SELECT ability_code, ability_enum,ability_id,operator_id
        FROM tb_ability
        where is_del=0
    </select>
</mapper>
