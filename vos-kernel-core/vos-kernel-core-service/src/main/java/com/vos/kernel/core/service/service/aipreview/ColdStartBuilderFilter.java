package com.vos.kernel.core.service.service.aipreview;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.vos.kernel.core.api.domain.TaskTypeColdEntity;
import com.vos.kernel.core.api.domain.TaskTypeEntity;
import com.vos.kernel.core.api.dto.*;
import com.vos.kernel.core.api.dto.params.ColdStartAbilityCallParamsDTO;
import com.vos.kernel.core.service.convert.AiCallConvertMapper;
import com.vos.kernel.core.service.service.batisplus.IAbilityService;
import com.vos.kernel.core.service.service.batisplus.ITaskTypeColdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 大模型冷启动参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/18
 * @description: com.vos.kernel.core.service.service.aipreview
 */
@Slf4j
@Service
public class ColdStartBuilderFilter implements AiPreViewBaseFilter {

    @Resource
    IAbilityService abilityService;

    @Resource
    AiCallConvertMapper aiCallConvertMapper;


    @Override
    public void doFilter(AiPreViewCallDTO aiPreViewCall, AiRuntimeContextDTO context, AiPreViewFilterChain filterChain) {
        AiColdActivationDTO aiColdActivation = new AiColdActivationDTO();

        ArrayList<AiClassInfoDTO> aiClassInfoList = new ArrayList<>();
        //判断是否是大模型生成的算法
        TaskTypeEntity ability = context.getAbility();
        aiColdActivation.setType(ability.getContains());
        //预览效果时才需要获取；任务运行时已经包含在配置内
        TaskTypeColdEntity bigModelGenerateAbilityConfig = context.getWaitAiResult() ? abilityService.getBigModelGenerateAbilityParams(ability.getId().toString()) : null;
        if (null != bigModelGenerateAbilityConfig) {
            JSONArray coldName = JSONArray.parseArray(bigModelGenerateAbilityConfig.getColdName());
            JSONArray threshold = JSONArray.parseArray(bigModelGenerateAbilityConfig.getColdThreshold());
            JSONArray positive = JSONArray.parseArray(bigModelGenerateAbilityConfig.getColdPositive());
            JSONArray negative = JSONArray.parseArray(bigModelGenerateAbilityConfig.getColdNegative());
            for (int j = 0; j < coldName.size(); j++) {
                AiClassInfoDTO aiClassInfo = new AiClassInfoDTO();
                aiClassInfo.setName(coldName.get(j).toString());
                aiClassInfo.setThreshold(Float.parseFloat(threshold.get(j).toString()));
                aiClassInfo.setFineGrainClassesPositive(JSON.parseArray(positive.get(j).toString(), String.class));
                aiClassInfo.setFineGrainClassesNegative(JSON.parseArray(negative.get(j).toString(), String.class));
                aiClassInfoList.add(aiClassInfo);
            }
        }
        //判断是否传递了大模型验证参数
        AbilityConfigDTO abilityConfig = aiPreViewCall.getAbilityConfig();
        if (abilityConfig != null && CollectionUtil.isNotEmpty(abilityConfig.getColdStartConfig())) {
            List<ColdStartAbilityCallParamsDTO> coldStartConfig = abilityConfig.getColdStartConfig();
            for (ColdStartAbilityCallParamsDTO c : coldStartConfig) {
                AiClassInfoDTO aiClassInfoDTO = aiCallConvertMapper.coldStartParamsConvert(c);
                aiClassInfoList.add(aiClassInfoDTO);
            }
        }
        aiColdActivation.setIncludeClasses(aiClassInfoList);

        //参数拼装
        List<AiExecuteImageParamDTO> imageAiParamList = context.getImageAiParamList();
        for (AiExecuteImageParamDTO i : imageAiParamList) {
            ImageAiParamDTO imageAiParam = i.getImageAiParam();
            imageAiParam.setColdActivation(aiColdActivation);
        }

        //执行后续链路
        context.setPos(context.getPos() + 1);
        filterChain.doFilter(aiPreViewCall, context, filterChain);
    }
}
