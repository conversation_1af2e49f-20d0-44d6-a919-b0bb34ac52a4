package com.vos.kernel.core.service.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.vos.kernel.common.constant.RedisKey;
import com.vos.kernel.common.dubbo.DubboBaseException;
import com.vos.kernel.core.api.domain.TaskCountEntity;
import com.vos.kernel.core.api.dto.params.VectorDeleteParam;
import com.vos.kernel.core.api.rpc.VectorRpcService;
import com.vos.kernel.core.service.entity.VectorCountEntity;
import com.vos.kernel.core.service.entity.VectorSourceEntity;
import com.vos.kernel.core.service.service.FusionService;
import com.vos.kernel.core.service.service.batisplus.IVectorCountService;
import com.vos.kernel.core.service.service.batisplus.IVectorSourceService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

@RefreshScope
@Component
@Slf4j
@EnableAsync(proxyTargetClass = true)
public class VectorTask {

    @Value("${vector.delete.minute}")
    private Integer vectorDeleteMinute;

    @Autowired
    FusionService fusionService;
    @Resource
    IVectorSourceService vectorSourceService;
    @Resource
    IVectorCountService iVectorCountService;
    @Resource
    StringRedisTemplate stringRedisTemplate;

    @Resource
    VectorRpcService vectorRpcService;

    /**
     * 删除向量
     */
    @XxlJob(value = "deleteVector")
    public ReturnT<String> deleteVector() {
        VectorDeleteParam vectorDeleteParam = new VectorDeleteParam();
        Date now = new Date();
        Date offsetDay = DateUtil.offsetMinute(now, -(vectorDeleteMinute));
        String timeString = "createTime" + " < " + "'" + DateUtil.formatDateTime(offsetDay) + "'";
        vectorDeleteParam.setFilterStr(timeString);
        List<VectorSourceEntity> vectorSourceEntityList = vectorSourceService.list();
        vectorSourceEntityList.forEach(vectorSourceEntity -> {
            vectorRpcService.deleteVector(vectorSourceEntity.getSourceId(), vectorDeleteParam);
        });
        return ReturnT.SUCCESS;
    }

    //    @Scheduled(cron = "0 0/1 * * * ?")
    @XxlJob(value = "vectorCount")
    public ReturnT<String> vectorCount() {
        String keyCount = RedisKey.VECTOR_COUNT + "*" + ":" + "*" + ":" + "*";
        Set<String> keys = stringRedisTemplate.keys(keyCount);
        log.debug("获取统计rediskey{}", JSONObject.toJSONString(keys));
        if (keys.isEmpty()) {
            return null;
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDate localDate = now.toLocalDate();
        List<VectorCountEntity> taskCountList = new ArrayList<>();
        keys.stream().forEach(key -> {
            Map<Object, Object> map = stringRedisTemplate.opsForHash().entries(key);
            String[] split = key.split(":");
            Integer h = Integer.valueOf(split[4]);
            LocalDate ymd = LocalDate.parse(split[3]);
            VectorCountEntity taskCount = new VectorCountEntity();
            taskCount.setOrgCode(split[1]);
            taskCount.setSourceId(split[2]);
            taskCount.setYmd(ymd);
            taskCount.setH(h);
            taskCount.setTotal(Integer.parseInt(Optional.ofNullable(map.get("total")).orElse(0).toString()));
            taskCount.setSuccessCount(Integer.parseInt(Optional.ofNullable(map.get("successCount")).orElse(0).toString()));
            taskCount.setFailCount(Integer.parseInt(Optional.ofNullable(map.get("failCount")).orElse(0).toString()));
            taskCountList.add(taskCount);
            if (localDate.isAfter(ymd)) {
                stringRedisTemplate.delete(key);
            }
        });
        List<List<VectorCountEntity>> countLists = Lists.partition(taskCountList, 1000);
        for (List<VectorCountEntity> taskCounts : countLists) {
            iVectorCountService.insertOrUpdateBatch(taskCounts);
        }
        return ReturnT.SUCCESS;
    }
}
