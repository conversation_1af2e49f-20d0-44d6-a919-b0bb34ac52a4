package com.vos.kernel.core.service.service.aipreview;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.vos.kernel.core.api.domain.ComparisonIndexEntity;
import com.vos.kernel.core.api.domain.TaskTypeEntity;
import com.vos.kernel.core.api.dto.AiExecuteImageParamDTO;
import com.vos.kernel.core.api.dto.AiPreViewCallDTO;
import com.vos.kernel.core.api.dto.AiRuntimeContextDTO;
import com.vos.kernel.core.service.service.batisplus.IAbilityService;
import com.vos.kernel.core.service.service.batisplus.IComparisonIndexService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 比对算法处理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/2
 * @description: com.vos.kernel.core.service.service.aipreview
 */
@Slf4j
@Service
public class ComparisonAbilityHandler implements AiPreViewBaseFilter {

    @Resource
    IAbilityService abilityService;

    @Override
    public void doFilter(AiPreViewCallDTO aiPreViewCall, AiRuntimeContextDTO context, AiPreViewFilterChain filterChain) {
        TaskTypeEntity ability = context.getAbility();
        Boolean isMatchType = ability.getIsMatchType();
        //比对算法的，需要添加Index参数
        if (BooleanUtil.isTrue(isMatchType) && StrUtil.isBlank(context.getIndexId())) {
            //正反向样本库设置
            String abilityId = aiPreViewCall.getAbilityId();
            String operator = aiPreViewCall.getOperator();
            String indexId = "";
            ComparisonIndexEntity comparisonIndexEntity = abilityService.getComparisonIndexEntity(abilityId, operator);
            if (comparisonIndexEntity != null) {
                indexId = comparisonIndexEntity.getIndexId().toString();
            }
            if (StrUtil.isNotBlank(indexId)) {
                List<AiExecuteImageParamDTO> imageAiParamList = context.getImageAiParamList();
                for (AiExecuteImageParamDTO p : imageAiParamList) {
                    p.setIndexId(indexId);
                }
            }
        }

        //执行后续链路
        context.setPos(context.getPos() + 1);
        filterChain.doFilter(aiPreViewCall, context, filterChain);
    }


}
