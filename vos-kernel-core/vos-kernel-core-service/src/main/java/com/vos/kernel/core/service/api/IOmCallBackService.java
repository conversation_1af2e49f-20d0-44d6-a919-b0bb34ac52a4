package com.vos.kernel.core.service.api;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.log.LogStrategy;
import com.github.lianjiatech.retrofit.spring.boot.log.Logging;
import com.vos.kernel.core.api.dto.ApiOutReturnDTO;
import com.vos.kernel.core.api.dto.OmCallBackDTO;
import retrofit2.http.Body;
import retrofit2.http.POST;
import retrofit2.http.Url;

/**
 * api输出接口定义
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/04/19
 * @description: com.vos.kernel.core.service.api
 */
@RetrofitClient(baseUrl = "http://omCallBack", errorDecoder = ErrorHandler.class)
@Logging(logStrategy = LogStrategy.BASIC)
public interface IOmCallBackService {

    /**
     * 请求api输出接口
     *
     * @param url
     * @param omCallBackDTO
     * @return
     */
    @POST
    ApiOutReturnDTO omCallBackRequest(@Url String url, @Body OmCallBackDTO omCallBackDTO);
}
