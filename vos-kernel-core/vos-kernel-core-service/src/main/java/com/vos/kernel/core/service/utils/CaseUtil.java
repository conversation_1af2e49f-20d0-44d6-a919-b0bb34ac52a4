package com.vos.kernel.core.service.utils;

import lombok.extern.slf4j.Slf4j;

/**
 *
 */
@Slf4j
public class CaseUtil {
    /**
     * 半角转全角的函数(SBC case)
     * 全角空格为12288,半角空格为32，其他字符半角(33-126)与全角(65281-65374)的对应关系是：均相差65248
     *
     * @param input 任意字符串
     * @return 全角字符串
     */
    public static String toSbc(String input) {
        if (StringUtils.isBlank(input)) {
            return "";
        }
        char[] c = input.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == 32) {
                c[i] = (char) 12288;
                continue;
            }
            if (c[i] < 127) {
                c[i] = (char) (c[i] + 65248);
            }
        }
        return new String(c);
    }

    /**
     * 全角转半角的函数(DBC case)
     * 全角空格为12288，半角空格为32 其他字符半角(33-126)与全角(65281-65374)的对应关系是：均相差65248
     *
     * @param input 任意字符串
     * @return 半角字符串
     */
    public static String toDbc(String input) {
        if (StringUtils.isBlank(input)) {
            return "";
        }
        char[] c = input.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == 12288) {
                c[i] = (char) 32;
                continue;
            }
            if (c[i] > 65280 && c[i] < 65375) {
                c[i] = (char) (c[i] - 65248);
            }
        }
        return new String(c);
    }

    public static void main(String[] args) {
        System.out.println(toDbc("法尔算法："));
        System.out.println(toSbc("法尔算法："));
    }
}
