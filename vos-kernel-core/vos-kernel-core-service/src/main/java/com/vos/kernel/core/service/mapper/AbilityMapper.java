package com.vos.kernel.core.service.mapper;

import com.vos.kernel.core.api.domain.AbilityEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 能力表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-19
 */
@Mapper
public interface AbilityMapper extends BaseMapper<AbilityEntity> {

    List<AbilityEntity> selectByAbilityEnum(@Param("abilityEnum") Integer abilityEnum, @Param("orgCode") String orgCode);
}
