package com.vos.kernel.core.service.system;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * cas用户信息
 */
@Data
public class UserInfoCAS implements Serializable {
    private UserInfo userInfo;
    @Data
    @Accessors(chain = true)
    public class UserInfo implements Serializable{
        private String userId;
        private String userCode;
        private String userName;
        private String orgCode;
        private String orgName;
        private String nickName;
    }
    private String ticket;

    public void setUserInfo(String userId,String userName,String orgCode, String nickName){
        UserInfo userInfo = new UserInfo()
                .setUserId(userId)
                .setUserName(userName)
                .setNickName(nickName)
                .setOrgCode(orgCode);
        this.userInfo = userInfo;
    }
}
/**
 * {
 *   "userInfo": {
 *     "userId": "4d24237a-61e0-4f89-9404-a6e57d9965b4",
 *     "userCode": "sl10104",
 *     "userName": "系统管理员",
 *     "orgCode": "10104",
 *     "orgName": "New政务大脑"
 *   },
 *   "ticket": "ST-4741-nqejYTY67KF7WrfmkkF4-sso.lh.org"
 * }
 */