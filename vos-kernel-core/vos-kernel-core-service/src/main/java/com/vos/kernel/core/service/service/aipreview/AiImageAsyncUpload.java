package com.vos.kernel.core.service.service.aipreview;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.linker.omos.client.domain.CommonConstant;
import com.vos.kernel.core.api.domain.VideoTaskResultEntity;
import com.vos.kernel.core.api.dto.*;
import com.vos.kernel.core.api.entity.constants.RocketMqTopic;
import com.vos.kernel.core.service.service.IAiImageRedisSpeedProcessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * redis图片信息异步落盘处理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/22
 * @description: com.vos.kernel.core.service.service.aipreview
 */
@Slf4j
@Service
public class AiImageAsyncUpload implements AiPreViewBaseFilter {

    @Resource
    RocketMQTemplate rocketMQTemplate;

    @Resource
    IAiImageRedisSpeedProcessService aiImageRedisSpeedProcessService;

    @Override
    public void doFilter(AiPreViewCallDTO aiPreViewCall, AiRuntimeContextDTO context, AiPreViewFilterChain filterChain) {
        //任务运行状态下，ai下发为redis图片信息的，需要异步落盘
        if (!BooleanUtil.isTrue(context.getWaitAiResult())) {

            List<VideoTaskResultEntity> videoTaskResultEntities = context.getVideoTaskResultEntities();
            if (CollectionUtil.isNotEmpty(videoTaskResultEntities)) {
                for (VideoTaskResultEntity result : videoTaskResultEntities) {
                    String keyImage = result.getKeyImage();
                    Long resultId = result.getId();
                    if (StrUtil.isNotBlank(keyImage) && !keyImage.contains(CommonConstant.HTTP) && BooleanUtil.isFalse(aiPreViewCall.getImageBase64Cached())) {
                        aiImageRedisSpeedProcessService.initSpeed(resultId.toString(), keyImage);
                        //redis加速图片读写
                        AiImageAsyncUploadDTO aiImageAsyncUploadDTO = new AiImageAsyncUploadDTO(keyImage, resultId, aiPreViewCall.getOperator());
                        rocketMQTemplate.syncSend(RocketMqTopic.IMAGE_ASYNC_TOPIC, MessageBuilder.withPayload(aiImageAsyncUploadDTO).build());
                    }
                }
            }
        }

        //执行后续链路
        context.setPos(context.getPos() + 1);
        filterChain.doFilter(aiPreViewCall, context, filterChain);
    }
}
