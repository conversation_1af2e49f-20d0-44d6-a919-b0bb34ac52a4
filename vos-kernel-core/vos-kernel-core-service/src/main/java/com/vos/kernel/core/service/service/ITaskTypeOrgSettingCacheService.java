package com.vos.kernel.core.service.service;

import com.vos.kernel.core.api.dto.AbilityConfigForShowDTO;

/**
 * 带缓存功能的组织AI配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/21
 * @description: com.vos.kernel.core.service.service
 */
public interface ITaskTypeOrgSettingCacheService {

    /**
     * 获取租户AI配置信息
     *
     * @param abilityId
     * @param operatorId
     * @return
     */
    AbilityConfigForShowDTO getOperatorAiConfig(String abilityId, String operatorId, Boolean fuseGetBse);

    /**
     * 删除配置
     *
     * @param abilityId
     * @param operatorId
     * @return
     */
    Boolean deleteAiConfig(String abilityId, String operatorId);

}
