package com.vos.kernel.core.service.service.batisplus.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vos.kernel.core.api.domain.TCallBackCountDay;
import com.vos.kernel.core.service.mapper.TCallBackCountDayMapper;
import com.vos.kernel.core.service.service.batisplus.ITCallBackCountDayService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@Service
public class TCallBackCountDayServiceImpl extends ServiceImpl<TCallBackCountDayMapper, TCallBackCountDay> implements ITCallBackCountDayService {

}
