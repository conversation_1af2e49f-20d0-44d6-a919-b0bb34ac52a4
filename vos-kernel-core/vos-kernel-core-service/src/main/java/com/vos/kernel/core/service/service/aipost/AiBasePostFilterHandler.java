package com.vos.kernel.core.service.service.aipost;

import com.vos.kernel.core.api.dto.AiPostFilterContextDTO;
import com.vos.kernel.core.api.dto.params.AiCallResultDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/20
 * @description: com.vos.kernel.core.service.service.aipost
 */
public interface AiBasePostFilterHandler {

    /**
     * 具体执行逻辑
     *
     * @param aiResult
     * @param context
     * @param postChain
     */
    public void handle(AiCallResultDTO aiResult, AiPostFilterContextDTO context, AiPostFilterChain postChain);
}
