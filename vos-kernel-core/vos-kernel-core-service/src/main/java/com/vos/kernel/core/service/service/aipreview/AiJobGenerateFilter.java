package com.vos.kernel.core.service.service.aipreview;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.collect.Lists;
import com.linker.basic.utils.IdWorker;
import com.linker.omos.client.domain.CommonConstant;
import com.vos.kernel.common.dubbo.DubboBaseException;
import com.vos.kernel.common.enums.CallBackTypeEnum;
import com.vos.kernel.common.enums.FileTypeEnum;
import com.vos.kernel.common.enums.RpcCallBackInfoEnum;
import com.vos.kernel.common.utils.DateUtil;
import com.vos.kernel.core.api.domain.AbilityEntity;
import com.vos.kernel.core.api.domain.TaskTypeEntity;
import com.vos.kernel.core.api.dto.*;
import com.vos.kernel.core.api.dto.params.AbilityVideoStreamInternalConfigParamsDTO;
import com.vos.kernel.core.service.enums.RailWayMethodEnum;
import com.vos.kernel.core.service.service.IApiAbilityExecuteContextService;

import com.vos.task.automated.api.model.entity.TaskServerRouterEntity;
import com.vos.task.automated.api.service.rpc.IServingRouterRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ai能力简单编排(串行处理)
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/19
 * @description: com.vos.kernel.core.service.service.aipreview
 */
@Slf4j
@Service
public class AiJobGenerateFilter implements AiPreViewBaseFilter {

    /**
     * 效果预览回调地址
     * http://newhuizhi-java:9997/callback/task
     */
    @Value("${task.scheduler.callback.url}")
    private String callbackUrl;

    /**
     * 任务运行回调地址
     */
    @Value("${task.scheduler.callback.url2}")
    private String callbackUrl2;

    /**
     * rpc回调还是http请求回调
     */
    @Value("${task.scheduler.rpcCallback:true}")
    private Boolean rpcCallback;

    /**
     * 视频流回调地址
     */
    @Value("${task.scheduler.callback.url3}")
    private String callbackUrl3;

    @Value("${ability.needDest:#{null}}")
    private String needDest;

    private static final String ATOM_CALLBACK_URL_MASK = "/vql/v1/serving/process";

    @DubboReference
    private IServingRouterRpcService servingRouterRpcService;

    @Resource
    IApiAbilityExecuteContextService apiAbilityExecuteContextService;

    @Override
//    @LogMetric
    public void doFilter(AiPreViewCallDTO aiPreViewCall, AiRuntimeContextDTO context, AiPreViewFilterChain filterChain) {
        boolean isVideaStream = StrUtil.isNotBlank(context.getConfigCode())
                && FileTypeEnum.STREAM.equals(context.getImageType());
        AiJobInfoDTO aiJobInfo = generateJobMainInfo(context,
                !BooleanUtil.isTrue(context.getWaitAiResult()),
                context.getApiAppSourceId(),
                isVideaStream);

        TaskTypeEntity ability = context.getAbility();
        String abilityCode = ability.getAbility();
        List<String> abilityCodes = StrUtil.split(abilityCode, ',');
        List<AbilityEntity> abilityTask = context.getAbilityTask();
        Map<String, AbilityEntity> abilityPkIdMap = abilityTask.stream().collect(Collectors.toMap(AbilityEntity::getAbilityId, Function.identity()));
        log.info("进入任务拼装阶段,能力列表：{}", JSON.toJSONString(abilityCodes));
        String videoId = aiPreViewCall.getVideoId();
        String videoValidation = String.valueOf(IdWorker.nextId());

        ArrayList<String> dealTypes = new ArrayList<>();
        List<AiJobDetailDTO> aiJobDetails = new ArrayList<>();

        boolean isFirst = true;
        String parentTaskId = "";
        String subSnowId = String.valueOf(IdWorker.nextId());
        int i = 1;
        for (String a : abilityCodes) {
            AbilityEntity abilityEntity = abilityPkIdMap.get(a);
            String abilityDealType = abilityEntity.getAbilityDealType();
            dealTypes.add(abilityDealType);
            //构造ai真实任务主体
            AiJobDetailDTO aiJobDetail = buildJobDetailInfo(context.getWaitAiResult(), context.getTaskOrder());
            aiJobDetail.setVideoId(videoId);
            ExtendJsonDTO extendJson = new ExtendJsonDTO();
            extendJson.setSourceId(videoId + "_" + videoValidation);
            extendJson.setIsSequential(false);
            aiJobDetail.setExtend(extendJson);
            aiJobDetail.setSubSnowId(subSnowId);
            //构造额外信息
            buildJobDetailExtraInfo(aiJobDetail, abilityEntity);
            //第一个为父；父子关系生成
            if (BooleanUtil.isFalse(isFirst)) {
                //子
                aiJobDetail.setIsFirst(0);
                aiJobDetail.setIsLast(abilityCodes.size() == i ? 1 : 0);
                aiJobDetail.setRequestJson("");
            } else {//父
                aiJobDetail.setIsLast(abilityCodes.size() == 1 ? 1 : 0);
                aiJobDetail.setRequestJson(isVideaStream ? buildVideoStreamRequestJson(context, aiPreViewCall, abilityEntity.getServerUrl())
                        : buildImageRequestJson(context, aiPreViewCall));
            }
            if (StrUtil.isNotBlank(parentTaskId)) {
                aiJobDetail.setParentBusinessIds(parentTaskId);
            }
            parentTaskId = aiJobDetail.getBusinessId();
            isFirst = false;
            i++;
            aiJobDetails.add(aiJobDetail);
        }

        //ai任务主体绑定
        aiJobInfo.setSubList(aiJobDetails);
        //处理类型
        String dealTypeStr = dealTypes.stream().distinct().collect(Collectors.joining(","));
        aiJobInfo.setAbilityDealTypes(dealTypeStr);
        context.setAiJobInfo(aiJobInfo);
        //执行后续链路
        context.setPos(context.getPos() + 1);
        filterChain.doFilter(aiPreViewCall, context, filterChain);
    }

    /**
     * 生成AI调度任务主体信息
     *
     * @param context        上下文
     * @param isTaskRuntime  是否任务运行时
     * @param apiAppSourceId api能力调用时
     * @return
     */
    private AiJobInfoDTO generateJobMainInfo(AiRuntimeContextDTO context,
                                             Boolean isTaskRuntime,
                                             String apiAppSourceId,
                                             boolean isVideoStream) {
        AiJobInfoDTO aiJobInfo = context.getAiJobInfo();
        aiJobInfo.setTaskType(1);
        if (BooleanUtil.isTrue(rpcCallback)) {
            aiJobInfo.setCallbackType(CallBackTypeEnum.RPC.getKey());
            //rpc形式，传递
            Integer rpcKey = isVideoStream ? RpcCallBackInfoEnum.VIDEO.getKey()
                    : (BooleanUtil.isTrue(isTaskRuntime) ? RpcCallBackInfoEnum.TASK.getKey() : RpcCallBackInfoEnum.PREVIEW.getKey());
            aiJobInfo.setCallbackInfo(rpcKey.toString());
        } else {
            aiJobInfo.setCallbackType(CallBackTypeEnum.HTTP.getKey());
            //http形式传递回调地址
            aiJobInfo.setCallbackInfo(isVideoStream ? callbackUrl3
                    : (BooleanUtil.isTrue(isTaskRuntime) ? callbackUrl2 : callbackUrl));

        }
        //api能力调用时，需要设置能力调用与api调用之间的关系
        if (StrUtil.isNotBlank(apiAppSourceId)) {
            if (BooleanUtil.isTrue(context.getApiCallMulti())) {
                //多任务的对应父子关系
                apiAbilityExecuteContextService.setAbilityExecuteRelationWithApi(apiAppSourceId, aiJobInfo.getAppSourceId());
            } else {
                //单任务的使用主接口唯一值
                aiJobInfo.setAppSourceId(apiAppSourceId);
            }
        }
        return aiJobInfo;
    }

    /**
     * 构造ai真实任务详情
     *
     * @return
     */
    private AiJobDetailDTO buildJobDetailInfo(Boolean waitToAi, Boolean taskOrder) {
        AiJobDetailDTO aiJobDetail = new AiJobDetailDTO();
        aiJobDetail.setBusinessId(String.valueOf(IdWorker.nextId()));
        if (ObjectUtil.isNotEmpty(taskOrder)) {
            aiJobDetail.setTaskOrder(BooleanUtil.isTrue(taskOrder) ? 1 : 0);
        } else {
            aiJobDetail.setTaskOrder(BooleanUtil.isTrue(waitToAi) ? 1 : 0);
        }
        aiJobDetail.setIsFirst(1);
        aiJobDetail.setIsLast(1);
        aiJobDetail.setIsJump(0);
        return aiJobDetail;
    }

    /**
     * 构造额外信息
     *
     * @param aiJobDetail
     * @param ability
     */
    private void buildJobDetailExtraInfo(AiJobDetailDTO aiJobDetail, AbilityEntity ability) {

        aiJobDetail.setAbilityId(String.valueOf(ability.getOperatorId()));
        aiJobDetail.setHzAbilityId(ability.getId());
        aiJobDetail.setOmAppId(ability.getAbilityId());
//        aiJobDetail.setSubSnowId(String.valueOf(IdWorker.nextId()));
//        aiJobDetail.setSubBusinessId(aiJobDetail.getBusinessId());
        aiJobDetail.setDependPreset(ability.getDependPreset());
    }

    /**
     * 图片参数
     *
     * @return
     */
    private String buildImageRequestJson(AiRuntimeContextDTO context, AiPreViewCallDTO aiPreViewCall) {
        //图片参数处理
        List<AiExecuteImageParamDTO> imageAiParamList = context.getImageAiParamList();
        List<AiExecuteImageParamDTO> filter = new ArrayList<>();
        List<String> filterImages = context.getFilterImages() == null ? new ArrayList<>() : context.getFilterImages();
        //过滤无需请求ai的图片
        for (AiExecuteImageParamDTO a : imageAiParamList) {
            a.setImageAiParam(null);
            if (!filterImages.contains(a.getData())) {
                a.setOrgId(aiPreViewCall.getOperator());
                filter.add(a);
            }
        }
        if (CollectionUtil.isEmpty(filter)) {
            log.info("前置过滤后。无任何图片存在，不再请求ai");
            context.setSkip(true);
            context.setNeedClearImage(true);
        }
        //图片Ai参数拼装
        AiJobParamsDTO aiJobParams = new AiJobParamsDTO();
        //特殊算法需要上传图片
        if (StrUtil.isNotEmpty(needDest) && needDest.contains(context.getAbility().getActionId())) {
            aiJobParams.setDest(Collections.singletonList(context.getAiResourceDest()));
        } else {
            aiJobParams.setDest(new ArrayList<>());
        }
        aiJobParams.setSrc(filter);
        aiJobParams.setTaskId(aiPreViewCall.getTaskId());
        JSONObject kwargs = new JSONObject();
        if (BooleanUtil.isFalse(context.getWaitAiResult()) && aiPreViewCall.getSampleStatus()
                != null && 1 == aiPreViewCall.getSampleStatus()) {
            //任务状态下，样本进行中；针对棚顶算法
            kwargs.put("taskType", RailWayMethodEnum.SAMPLE.getMethod());
        } else {
            kwargs.put("taskType", RailWayMethodEnum.DETECT.getMethod());
        }
        aiJobParams.setKwargs(kwargs);
        return JSON.toJSONString(aiJobParams, SerializerFeature.DisableCircularReferenceDetect);
    }

    /**
     * 视频流参数
     *
     * @return
     */
    private String buildVideoStreamRequestJson(AiRuntimeContextDTO context, AiPreViewCallDTO aiPreViewCall, String url) {
        List<String> imageList = aiPreViewCall.getImageList();
        List<String> imageIds = aiPreViewCall.getImageIds();
        List<AbilityVideoStreamInternalConfigParamsDTO> internalConfig = aiPreViewCall.getAbilityConfig().getVideoStreamInternalConfig();
        int index = 0;

        if (!url.contains(CommonConstant.HTTP)) {
            //主从部署负载
            TaskServerRouterEntity servingRoute = servingRouterRpcService.getServingRoute(url);
            if (servingRoute == null) {
                throw new DubboBaseException("该能力没找到对应服务器");
            }
            String slavePods = servingRoute.getSlavePods();

            JSONArray jsonArray = JSONArray.parseArray(slavePods);

            int i = jsonArray.size();
            String pod = jsonArray.getString(i - 1);

            url = CommonConstant.HTTP_PROTOCOL + pod + "-c-s.ai:8000";
        }

        List<AiExecuteImageParamDTO> src = Lists.newArrayList();
        for (String image : imageList) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("config_code", context.getConfigCode());
            internalConfig.forEach(config -> {
                jsonObject.put(config.getKey(), config.getValue());
            });

            String atomCallbackUrl = "";
            if (StrUtil.isNotBlank(url)) {
                atomCallbackUrl = url.replaceAll(ATOM_CALLBACK_URL_MASK, "");
            }

            AiExecuteImageParamDTO dto = new AiExecuteImageParamDTO().setSrcType("stream")
                    .setData(image)
                    .setVideoId(aiPreViewCall.getVideoId())
                    .setEventTime(DateUtil.getSimpleYMDHMS(LocalDateTime.now()))
                    .setTaskId(context.getConfigCode())
                    .setKwargs(jsonObject)
                    .setImageId(imageIds.get(index))
                    .setOrgId(aiPreViewCall.getOperator())
                    .setCallbackUrl(callbackUrl3)
                    .setAtomCallBackUrl(atomCallbackUrl)//atomCallbackUrl
                    .setExceptionCallBackUrl(context.getExceptionCallBackUrl());
            src.add(dto);
            index++;
        }

        JSONObject kwargs = new JSONObject();
        kwargs.put("appKey", context.getApiAppSourceId());
        kwargs.put("ability", context.getAbility());
        kwargs.put("abilityId", context.getAbility().getAbility());
        AiJobParamsDTO aiJobParams = new AiJobParamsDTO().setSrc(src)
                .setTaskId(aiPreViewCall.getTaskId())
                .setDest(Collections.emptyList())
                .setKwargs(new JSONObject());
        return JSON.toJSONString(aiJobParams, SerializerFeature.DisableCircularReferenceDetect);
    }
}
