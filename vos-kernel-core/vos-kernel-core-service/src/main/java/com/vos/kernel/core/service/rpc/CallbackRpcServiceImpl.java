package com.vos.kernel.core.service.rpc;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.vos.kernel.common.utils.SnowFlakeUtil;
import com.vos.kernel.core.api.dto.*;
import com.vos.kernel.core.api.dto.params.AiCallResultDTO;
import com.vos.kernel.core.api.entity.constants.RocketMqTopic;
import com.vos.kernel.core.api.rpc.ICallBackRpcService;
import com.vos.kernel.core.service.convert.AiCallConvertMapper;
import com.vos.kernel.core.service.enums.SrcTypeEnum;
import com.vos.kernel.core.service.service.IAliOssService;
import com.vos.kernel.core.service.service.ITaskSendCacheService;
import com.vos.kernel.core.service.service.aipost.AiPostFilterChain;
import com.vos.kernel.core.service.utils.WaterMarkUtils;

import org.apache.dubbo.config.annotation.DubboService;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@DubboService
@RefreshScope
public class CallbackRpcServiceImpl implements ICallBackRpcService {
    @Resource
    ITaskSendCacheService taskSendCacheService;

    @Resource
    AiPostFilterChain aiPostFilterChain;

    @Resource
    AiCallConvertMapper aiCallConvertMapper;

    @Resource
    IAliOssService aliOssService;

    @Value("${ability.openTrace:false}")
    private Boolean openTrace;

    @Resource
    RocketMQTemplate rocketMQTemplate;

    @Override
    @Async("callBackThreadPool")
    public void videoStreamCallback(JSONObject req) {
        AiCallResultDTO aiCallResult = parseResult(req);
        Boolean create = req.getBoolean("create");
        //推送mq
        VideoCallBackMqDTO videoCallBackMqDTO = new VideoCallBackMqDTO();

        videoCallBackMqDTO.setResults(aiCallResult.getResultList());
        videoCallBackMqDTO.setConfigCode(aiCallResult.getConfigCode());
        videoCallBackMqDTO.setApiBackUrl(BooleanUtil.isTrue(create) ? aiCallResult.getApiCallBackUrl() + "/create" : aiCallResult.getApiCallBackUrl());
        videoCallBackMqDTO.setAbilityId(req.getLong("abilityId"));
        rocketMQTemplate.syncSend(RocketMqTopic.VIDEO_CALL_BACK_TOPIC, MessageBuilder.withPayload(videoCallBackMqDTO).build());

        /*// 后置处理器处理视频流算法回调结果
        AiPostFilterContextDTO aiPostFilterContext = new AiPostFilterContextDTO();
        aiPostFilterContext.setPos(7);
        aiPostFilterContext.setOpenTrace(openTrace);
        aiPostFilterChain.handle(aiCallResult, aiPostFilterContext, aiPostFilterChain);*/
    }

    private AiCallResultDTO parseResult(JSONObject req) {
        log.info("视频流算法callback接口，{}", req);
        String appSourceId = "";

        // 数据处理，返回给业务端
        List<ResultListRespDTO> resultListResp = parseResults(req);

        for (ResultListRespDTO resultListRespDTO : resultListResp) {
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(resultListRespDTO.getKwargs()));
            appSourceId = StrUtil.isBlank(jsonObject.getString("config_code")) ? jsonObject.getString("task_id") : jsonObject.getString("config_code");
        }

        AiJobInfoDTO taskSendCache = taskSendCacheService.getTaskSendCacheVideo(appSourceId);
        log.debug("任务编号：{}  缓存数据CHECK：{} ,", appSourceId, taskSendCache.toString());

        log.debug("AI业务端处理数据：{}", resultListResp);
        AiCallResultDTO aiCallResult = aiCallConvertMapper.callCacheConvert(taskSendCache);
        aiCallResult.setAppSourceId(taskSendCache.getAppSourceId());
        aiCallResult.setResultList(resultListResp);
        aiCallResult.setImageList(taskSendCache.getOriginImageList());
        aiCallResult.setImageInfoList(taskSendCache.getOriginImageInfoList());
        aiCallResult.setOperatorId(StrUtil.isBlank(taskSendCache.getOperatorId()) ? "1" : taskSendCache.getOperatorId());
        aiCallResult.setAllAiParams(taskSendCache.getAiAllParams());
        return aiCallResult;
    }

    private List<ResultListRespDTO> parseResults(JSONObject req) {
        Integer code = Optional.ofNullable(req.getInteger("code")).orElse(0);
        if (code != 200) {
            log.error("视频流算法异步回调返回异常，req={}", JSON.toJSONString(req));
            return Collections.emptyList();
        }
        try {
            Set<String> hashSet = new HashSet<>();
            JSONObject results = req.getJSONObject("results");
            List<ResultListRespDTO> resultListRsps = new ArrayList<>();
            Set<String> videoSet = results.keySet();
            for (String videoId : videoSet) {
                JSONArray jsonArray = results.getJSONArray(videoId);
                List<ResultListRespDTO> respList = JSON.parseArray(JSON.toJSONString(jsonArray), ResultListRespDTO.class);
                // 结果去重
                respList = respList.stream().filter(each -> hashSet.add(each.getId())).collect(Collectors.toList());
                resultListRsps.addAll(respList);
            }
            // 将base64图片上传到服务器，否则编码长度太长，导致mq接收失败
            resultListRsps.forEach(resultListRsp -> {
                if (ObjectUtil.isNotEmpty(resultListRsp.getImage())) {
                    String imageUrlBase64 = resultListRsp.getImage();
                    // 上传图片
                    InputStream inputStream = null;
                    if (imageUrlBase64.startsWith("http") || imageUrlBase64.startsWith("https")) {
                        inputStream = WaterMarkUtils.getInputStream(SrcTypeEnum.URL.getCode(),
                                imageUrlBase64);
                        String path = new StringBuffer().append("photo/videoStream/").append(
                                SnowFlakeUtil.getId()).append(".jpg").toString();
                        resultListRsp.setImage(aliOssService.uploadOssInputStream(path, inputStream));
                    } else if (imageUrlBase64.startsWith("rtsp")) {

                    } else {
                        inputStream = WaterMarkUtils.getInputStream(SrcTypeEnum.B64.getCode(),
                                imageUrlBase64);
                        String path = new StringBuffer().append("photo/videoStream/").append(
                                SnowFlakeUtil.getId()).append(".jpg").toString();
                        resultListRsp.setImage(aliOssService.uploadOssInputStream(path, inputStream));
                    }
                }
            });
            return resultListRsps;
        } catch (Exception e) {
            log.error("视频流算法异步回调格式不对，req={}", JSON.toJSONString(req));
            return Collections.emptyList();
        }
    }
}