package com.vos.kernel.core.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/22
 * @description: com.vos.kernel.core.service.config
 */
@Data
@ConfigurationProperties(prefix = "ai-redis")
@Component
public class AiRedisAccelerateProperties {

    /**
     * 端口
     */
    private Integer port;

    /**
     * 数据库
     */
    private Integer database;

    /**
     * 主机
     */
    private String host;

    /**
     * 密码
     */
    private String password;


    /**
     * 获取连接
     *
     * @return
     */
    public String getSpeedConnection() {
        return "redis://:" + password + "@" + host + ":" + port + "/" + database + "/";
    }
}
