package com.vos.kernel.core.service.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.vos.kernel.core.api.domain.TaskTypeColdEntity;
import com.vos.kernel.core.api.domain.TaskTypeConfigAiEntity;
import com.vos.kernel.core.api.domain.TaskTypeOrgSettingEntity;
import com.vos.kernel.core.api.dto.AbilityConfigDTO;
import com.vos.kernel.core.api.dto.AbilityConfigForShowDTO;
import com.vos.kernel.core.api.dto.AiAdvancedDetailConfigDTO;
import com.vos.kernel.core.api.dto.params.AbilityBasicConfigParamsDTO;
import com.vos.kernel.core.api.dto.params.ColdStartAbilityCallParamsDTO;
import com.vos.kernel.core.api.dto.params.SynergyConfigParamsDTO;
import com.vos.kernel.core.service.enums.AiConfigExceptionEnum;
import com.vos.kernel.core.api.exception.AiConfigException;
import com.vos.kernel.core.service.service.ITaskTypeSettingCacheService;
import com.vos.kernel.core.service.service.IWaterMessageSettingCacheService;
import com.vos.kernel.core.service.service.batisplus.ITaskTypeColdService;
import com.vos.kernel.core.service.service.batisplus.ITaskTypeConfigAiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 带缓存功能的AI配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/21
 * @description: com.vos.kernel.core.service.service.impl
 */
@Slf4j
@Service
public class TaskTypeSettingCacheServiceImpl implements ITaskTypeSettingCacheService {

    @Resource
    StringRedisTemplate stringRedisTemplate;

    @Resource
    ITaskTypeConfigAiService taskTypeConfigAiService;

    @Resource
    ITaskTypeColdService taskTypeColdService;

    @Resource
    IWaterMessageSettingCacheService waterMessageSettingCacheService;

    /**
     * 缓存key
     */
    public static final String CACHE_KEY = "conf:origin:";

    /**
     * 冷启动
     */
    public static final String COLD_CACHE_KEY = "conf:origin:";

    @Override
    public AbilityConfigForShowDTO getOriginAiConfig(String abilityId, String operator) {
        //缓存获取
        String cacheKey = CACHE_KEY + abilityId + operator;
        log.info("获取配置信息 key:{}", cacheKey);
        String value = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StrUtil.isBlank(value)) {
            //数据库获取
            QueryWrapper<TaskTypeConfigAiEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(TaskTypeConfigAiEntity::getTaskTypeCode, abilityId)
                    .last("limit 1");
            TaskTypeConfigAiEntity one = taskTypeConfigAiService.getOne(queryWrapper);
            if (one != null) {
                //查询是否是大模型冷启动算法
                List<ColdStartAbilityCallParamsDTO> coldAbilityConfig = getColdAbilityConfig(abilityId);
                AbilityConfigForShowDTO abilityConfigDTO = buildConfig(one, coldAbilityConfig);
                //水印信息
                TaskTypeOrgSettingEntity userWaterMsgConf = waterMessageSettingCacheService.getUserWaterMsgConf(operator, abilityId);
                if (userWaterMsgConf != null) {
                    AbilityBasicConfigParamsDTO basicConfig = abilityConfigDTO.getBasicConfig();
                    basicConfig.setDrawAiOutputResultDesc(userWaterMsgConf.getMsgDesc());
                    basicConfig.setAiOutputResultDesc(userWaterMsgConf.getMsgWater());
                }
                //保存缓存
                stringRedisTemplate.opsForValue().set(cacheKey, JSONObject.toJSONString(abilityConfigDTO), 7, TimeUnit.DAYS);
                return abilityConfigDTO;
            } else {
                throw new AiConfigException(AiConfigExceptionEnum.NONE_ABILITY);
            }

        } else {
            return JSONObject.parseObject(value, AbilityConfigForShowDTO.class);
        }
    }

    @Override
    public List<ColdStartAbilityCallParamsDTO> getColdAbilityConfig(String abilityId) {
        //缓存获取
        String cacheKey = COLD_CACHE_KEY + abilityId;
        String value = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StrUtil.isBlank(value)) {
            //数据库获取
            TaskTypeColdEntity coldEntity = taskTypeColdService.getById(abilityId);
            if (coldEntity != null) {
                List<ColdStartAbilityCallParamsDTO> aiClassInfoList = new ArrayList<>();
                JSONArray coldName = JSONArray.parseArray(coldEntity.getColdName());
                JSONArray threshold = JSONArray.parseArray(coldEntity.getColdThreshold());
                JSONArray positive = JSONArray.parseArray(coldEntity.getColdPositive());
                JSONArray negative = JSONArray.parseArray(coldEntity.getColdNegative());
                for (int j = 0; j < coldName.size(); j++) {
                    ColdStartAbilityCallParamsDTO coldStart = new ColdStartAbilityCallParamsDTO();
                    coldStart.setName(coldName.get(j).toString());
                    coldStart.setColdThreshold(Float.parseFloat(threshold.get(j).toString()));
                    coldStart.setColdPositive(JSON.parseArray(positive.get(j).toString(), String.class));
                    coldStart.setColdNegative(JSON.parseArray(negative.get(j).toString(), String.class));
                    aiClassInfoList.add(coldStart);
                }
                //保存缓存
                stringRedisTemplate.opsForValue().set(cacheKey, JSONObject.toJSONString(aiClassInfoList), 7, TimeUnit.DAYS);
                return aiClassInfoList;
            } else {
                return new ArrayList<>();
            }
        } else {
            return JSONObject.parseArray(value, ColdStartAbilityCallParamsDTO.class);
        }
    }

    /**
     * 构造数据
     *
     * @param entity
     * @param coldAbilityConfig
     * @return
     */
    private AbilityConfigForShowDTO buildConfig(TaskTypeConfigAiEntity entity, List<ColdStartAbilityCallParamsDTO> coldAbilityConfig) {

        AbilityConfigForShowDTO abilityConfig = new AbilityConfigForShowDTO();
        abilityConfig.setColdStartConfig(coldAbilityConfig);
        AbilityBasicConfigParamsDTO abilityBasicConfig = new AbilityBasicConfigParamsDTO();
        abilityBasicConfig.setBelieve(Float.parseFloat(entity.getBelieve()));
        abilityBasicConfig.setDisplayBelieve(entity.getDisplayBelieve() == 1);
        abilityBasicConfig.setDisplayBox(entity.getDisplayBox() == 1);

        abilityConfig.setBasicConfig(abilityBasicConfig);

        SynergyConfigParamsDTO synergyConfigParams = new SynergyConfigParamsDTO();
        synergyConfigParams.setMagnify(Float.parseFloat(entity.getMagnify()));
        abilityConfig.setSynergyConfig(synergyConfigParams);
        //前端显示用
        if (StrUtil.isNotBlank(entity.getConfig())) {
            List<AiAdvancedDetailConfigDTO> aiAdvanced = JSON.parseArray(entity.getConfig(), AiAdvancedDetailConfigDTO.class);
            abilityConfig.setAdvancedConfig(aiAdvanced);
        }

        return abilityConfig;
    }
}
