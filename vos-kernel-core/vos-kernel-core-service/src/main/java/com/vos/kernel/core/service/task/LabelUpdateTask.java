package com.vos.kernel.core.service.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.vos.kernel.core.api.domain.TaskTypeConfigAiEntity;
import com.vos.kernel.core.api.dto.BBox;
import com.vos.kernel.core.api.dto.BboxTypeCodeDTO;
import com.vos.kernel.core.service.service.batisplus.ITaskTypeConfigAiService;
import com.vos.kernel.core.service.service.batisplus.IVideoTaskResultService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/22
 * @description: com.vos.kernel.core.service.task
 */

@Component
@Slf4j
@EnableAsync(proxyTargetClass=true)
public class LabelUpdateTask {

    @Resource
    private ITaskTypeConfigAiService taskTypeConfigAiService;
    @Resource
    private IVideoTaskResultService videoTaskResultService;


    /**
     * 	label定时更新
     */
    @XxlJob(value = "labelUpdate")
    public ReturnT<String> labelUpdate() {

        QueryWrapper<TaskTypeConfigAiEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .isNull(TaskTypeConfigAiEntity::getAlarmLabel)
                .isNotNull(TaskTypeConfigAiEntity::getTaskTypeCode);

        List<TaskTypeConfigAiEntity> list = taskTypeConfigAiService.list(queryWrapper);

        List<String> taskTypeCodes = list.stream().map(TaskTypeConfigAiEntity::getTaskTypeCode).collect(Collectors.toList());
        List<BboxTypeCodeDTO> videoTaskResultEntities = videoTaskResultService.getBoxbyTypeCode(taskTypeCodes);

        for (BboxTypeCodeDTO bboxTypeCodeDTO:videoTaskResultEntities) {
            String bBoxInfo = bboxTypeCodeDTO.getBBoxInfo();
            List<BBox> bboxList = JSONArray.parseArray(bBoxInfo,BBox.class);
            String label= JSON.toJSONString(bboxList.get(0).getLabel());
            taskTypeConfigAiService.lambdaUpdate().set(TaskTypeConfigAiEntity::getAlarmLabel, label).eq(TaskTypeConfigAiEntity::getTaskTypeCode, bboxTypeCodeDTO.getAiType()).update();
        }
        return ReturnT.SUCCESS;
    }
}
