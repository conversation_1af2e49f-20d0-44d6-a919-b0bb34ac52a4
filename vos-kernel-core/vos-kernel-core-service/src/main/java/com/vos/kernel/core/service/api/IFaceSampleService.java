package com.vos.kernel.core.service.api;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.log.LogStrategy;
import com.github.lianjiatech.retrofit.spring.boot.log.Logging;
import com.vos.kernel.core.api.dto.AiUserFaceUploadRequestDTO;
import com.vos.kernel.core.api.dto.AiUserFaceUploadResultDTO;
import retrofit2.http.Body;
import retrofit2.http.DELETE;
import retrofit2.http.POST;
import retrofit2.http.Url;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/3
 * @description: com.vos.kernel.core.service.api
 */
@RetrofitClient(baseUrl = "${face.base_url}", errorDecoder = ErrorHandler.class, writeTimeoutMs = 1000 * 20, connectTimeoutMs = 1000 * 20, readTimeoutMs = 1000 * 20)
@Logging(logStrategy = LogStrategy.BASIC)
public interface IFaceSampleService {

    /**
     * 批量上传比对样本
     *
     * @param request
     * @return
     */
    @POST("vql/v1/upload/process")
    AiUserFaceUploadResultDTO uploadSampleInfoToAi(@Body AiUserFaceUploadRequestDTO request);

    @DELETE
    String deleteSampleToAi(@Url String url);

}
