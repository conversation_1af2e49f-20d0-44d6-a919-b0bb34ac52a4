package com.vos.kernel.core.service.service.impl;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.vos.kernel.core.api.domain.TaskTypeConfigOrgEntity;
import com.vos.kernel.core.api.domain.TaskTypeOrgSettingEntity;
import com.vos.kernel.core.api.dto.AbilityConfigDTO;
import com.vos.kernel.core.api.dto.AbilityConfigForShowDTO;
import com.vos.kernel.core.api.dto.AiAdvancedDetailConfigDTO;
import com.vos.kernel.core.api.dto.AiTargetAvoidIncludeConfigDTO;
import com.vos.kernel.core.api.dto.params.*;
import com.vos.kernel.core.service.event.CommonConfigChangeEvent;
import com.vos.kernel.core.service.service.ITaskTypeOrgSettingCacheService;
import com.vos.kernel.core.service.service.ITaskTypeSettingCacheService;
import com.vos.kernel.core.service.service.IWaterMessageSettingCacheService;
import com.vos.kernel.core.service.service.batisplus.ITaskTypeConfigOrgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 带缓存功能的组织AI配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/21
 * @description: com.vos.kernel.core.service.service.impl
 */
@Slf4j
@Service
public class TaskTypeOrgSettingCacheServiceImpl extends ConfigBuilder implements ITaskTypeOrgSettingCacheService {

    @Resource
    ITaskTypeConfigOrgService taskTypeConfigOrgService;

    @Resource
    ITaskTypeSettingCacheService taskTypeSettingCacheService;

    @Resource
    StringRedisTemplate stringRedisTemplate;

    @Resource
    IWaterMessageSettingCacheService waterMessageSettingCacheService;

    /**
     * 缓存数据
     */
    public static final String CACHE_KEY = "conf:operator:";

    @Override
    public AbilityConfigForShowDTO getOperatorAiConfig(String abilityId, String operatorId, Boolean fuseGetBse) {
        //缓存获取
        String cacheKey = CACHE_KEY + abilityId + operatorId;
        String value = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StrUtil.isBlank(value)) {
            AbilityConfigForShowDTO aiConfig;

            //数据库获取;没有获取到直接获取公共配置
            QueryWrapper<TaskTypeConfigOrgEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(TaskTypeConfigOrgEntity::getTaskTypeId, abilityId)
                    .eq(TaskTypeConfigOrgEntity::getIsDel, 0)
                    .eq(TaskTypeConfigOrgEntity::getOrgcode, operatorId).last("limit 1");
            TaskTypeConfigOrgEntity orgServiceOne = taskTypeConfigOrgService.getOne(queryWrapper);
            if (null == orgServiceOne) {
                aiConfig = taskTypeSettingCacheService.getOriginAiConfig(abilityId, operatorId);
                aiConfig.setHasEdite(false);
            } else {
                List<ColdStartAbilityCallParamsDTO> coldAbilityConfig = taskTypeSettingCacheService.getColdAbilityConfig(abilityId);
                aiConfig = buildOperatorAiConfig(orgServiceOne, coldAbilityConfig);
                aiConfig.setHasEdite(true);
                TaskTypeOrgSettingEntity userWaterMsgConf = waterMessageSettingCacheService.getUserWaterMsgConf(operatorId, abilityId);
                if (userWaterMsgConf != null) {
                    AbilityBasicConfigParamsDTO basicConfig = aiConfig.getBasicConfig();
                    basicConfig.setAiOutputResultDesc(userWaterMsgConf.getMsgWater());
                    basicConfig.setDrawAiOutputResultDesc(userWaterMsgConf.getMsgDesc());
                }
                //前端用
                AbilityConfigForShowDTO originAiConfig = taskTypeSettingCacheService.getOriginAiConfig(abilityId, operatorId);
                aiConfig.setAdvancedConfig(originAiConfig.getAdvancedConfig());
            }

            //保存缓存
            stringRedisTemplate.opsForValue().set(cacheKey, JSONObject.toJSONString(aiConfig), 7, TimeUnit.DAYS);
            return aiConfig;
        } else {
            return JSONObject.parseObject(value, AbilityConfigForShowDTO.class);
        }
    }

    @Override
    public Boolean deleteAiConfig(String abilityId, String operatorId) {
        TaskTypeConfigOrgEntity taskTypeConfigOrgEntity = new TaskTypeConfigOrgEntity();
        taskTypeConfigOrgEntity.setIsDel(1);

        QueryWrapper<TaskTypeConfigOrgEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TaskTypeConfigOrgEntity::getTaskTypeId, abilityId)
                .eq(TaskTypeConfigOrgEntity::getOrgcode, operatorId);
        taskTypeConfigOrgService.update(taskTypeConfigOrgEntity, queryWrapper);
        //删除缓存
        removeCache(new CommonConfigChangeEvent(operatorId, abilityId));
        return true;
    }

    @EventListener(CommonConfigChangeEvent.class)
    public void removeCache(CommonConfigChangeEvent event) {
        String cacheKey = CACHE_KEY + event.cacheKey();
        log.info("触发删除缓存：{},{}", event, cacheKey);
        stringRedisTemplate.delete(cacheKey);
    }

}
