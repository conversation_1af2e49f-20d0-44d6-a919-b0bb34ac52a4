package com.vos.kernel.core.service.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.vos.kernel.common.exception.LhException;
import com.vos.kernel.core.api.exception.SampleException;
import com.vos.kernel.core.api.exception.TUserException;
import com.vos.kernel.core.api.rpc.SampleLibraryFeedbackPhotoService;
import com.vos.kernel.core.api.rpc.TUserAuthenticationRpcService;
import com.vos.kernel.core.api.domain.SampleLibrary;
import com.vos.kernel.core.api.rpc.SampleLibraryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vos.kernel.core.api.vo.SampleListReq;
import com.vos.kernel.core.api.vo.SampleListResp;
import com.vos.kernel.core.service.enums.RailWayMethodEnum;
import com.vos.kernel.core.service.mapper.SampleLibraryMapper;
import com.vos.kernel.core.service.mapper.TaskTypeSampleMapper;
import com.vos.kernel.core.service.service.batisplus.ITaskTypeSampleService;
import com.vos.kernel.data.api.model.enumPackage.CalEnum;
import com.vos.kernel.data.api.model.enumPackage.SampleEnum;
import com.vos.kernel.data.api.rpc.TaskService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 样本库 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
@Transactional(rollbackFor = Exception.class)
@DubboService
public class SampleLibraryServiceImpl extends ServiceImpl<SampleLibraryMapper, SampleLibrary> implements SampleLibraryService {


    @DubboReference
    TUserAuthenticationRpcService tUserAuthenticationRpcService;
    @DubboReference
    TaskService taskService;

    @Resource
    SampleLibraryMapper sampleLibraryMapper;
    @Resource
    ITaskTypeSampleService taskTypeSampleService;
    @Resource
    TaskTypeSampleMapper taskTypeSampleMapper;
    @Resource
    TellAiService tellAiService;

    @Resource
    private SampleLibraryFeedbackPhotoService sampleLibraryFeedbackPhotoService;


    /**
     * @description: 添加正反向样本库，前置过滤库
     * @param:
     * @author: hejianbao
     * @date: 2022/4/26 10:40
     * @return:
     */
    @Override
    public Long saveSampleLibrary(SampleLibrary tSampleLibraryVo) {
        String orgCode = tSampleLibraryVo.getOrgId();
        if (null == orgCode) {
            throw new TUserException("用户授权异常");
        }
        if (ObjectUtil.isEmpty(tSampleLibraryVo.getSampleId())) {
            //每个算子只有一个样本库
            SampleLibrary sampleLibrary = this.getSampleLibrary(tSampleLibraryVo, orgCode);
            if (ObjectUtil.isNotEmpty(sampleLibrary)) {
                return sampleLibrary.getSampleId();
            }
            SampleLibrary tSampleLibrary = new SampleLibrary();
            BeanUtils.copyProperties(tSampleLibraryVo, tSampleLibrary);
            tSampleLibrary.setOrgId(orgCode);
            //样本库id
            tSampleLibrary.setCreateTime(new Date());
            sampleLibraryMapper.insert(tSampleLibrary);
            return tSampleLibrary.getSampleId();
        }
        return tSampleLibraryVo.getSampleId();
    }

    /**
     * @description: 获取样本库
     * @param:
     * @author: hejianbao
     * @date: 2022/4/26 10:48
     * @return:
     */
    @Override
    public SampleLibrary getSampleLibrary(SampleLibrary tSampleLibraryVo, String orgCode) {
        Integer sampleType = tSampleLibraryVo.getSampleType();
        String taskTypeCode = tSampleLibraryVo.getTaskTypeCode();
        String videoCode = tSampleLibraryVo.getVideoCode();
        Integer muTypeId = tSampleLibraryVo.getMuTypeId();
        if (null == sampleType) {
            new SampleException("500", "sampleType不能为空");
        }
        if (null == orgCode) {
            new SampleException("500", "orgCode不能为空");
        }
        if (null == taskTypeCode) {
            new SampleException("500", "taskTypeCode不能为空");
        }
        if (null == videoCode) {
            new SampleException("500", "videoCode不能为空");
        }
        LambdaQueryWrapper<SampleLibrary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SampleLibrary::getSampleType, sampleType)
                .eq(SampleLibrary::getOrgId, orgCode)
                .eq(SampleLibrary::getTaskTypeCode, taskTypeCode)
                .eq(SampleLibrary::getVideoCode, videoCode)
                .eq(SampleLibrary::getIsDel, 0)
                .eq(SampleLibrary::getMuTypeId, muTypeId)
                .last("limit 1");
        return sampleLibraryMapper.selectOne(queryWrapper);
    }

    /**
     * @description: 获取样本库
     * @param:
     * @author: hejianbao
     * @date: 2022/4/26 10:48
     * @return:
     */
    @Override
    public List<SampleLibrary> listSampleLibrary(String orgId, String videoCode, String taskTypeCode, Integer sampleType) {
        LambdaQueryWrapper<SampleLibrary> queryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotEmpty(orgId)) {
            queryWrapper.eq(SampleLibrary::getOrgId, orgId);
        }
        if (ObjectUtil.isNotEmpty(taskTypeCode)) {
            queryWrapper.eq(SampleLibrary::getTaskTypeCode, taskTypeCode);
        }
        if (ObjectUtil.isNotEmpty(videoCode)) {
            queryWrapper.eq(SampleLibrary::getVideoCode, videoCode);
        }
        if (ObjectUtil.isNotEmpty(sampleType)) {
            queryWrapper.eq(SampleLibrary::getSampleType, sampleType);
        }
        queryWrapper.eq(SampleLibrary::getIsDel, 0);
        return sampleLibraryMapper.selectList(queryWrapper);
    }

    @Override
    public List<SampleListResp> getSampleList(SampleListReq sampleListReq) {
//
        String orgCode = sampleListReq.getAuthenticationId();
        Integer status = sampleListReq.getStatus();
        Integer muTypeId = sampleListReq.getMuTypeId();

        // 在任务中 并且用到了这个应用
        List<SampleListResp> sampleList = taskTypeSampleMapper.getSampleList(status, orgCode, CalEnum.railway_ceiling.getAbility());
        // 判空
        if (CollectionUtil.isNotEmpty(sampleList)) {
            Map<String, SampleListResp> map = sampleList.stream().collect(Collectors.toMap(SampleListResp::getVideoCode, p -> p));
            // 视频id集合
            List<String> videoIdList = sampleList.stream().map(SampleListResp::getVideoCode).filter(each -> each != null).collect(Collectors.toList());
//            List<Long> sampleIdList = taskTypeSampleMapper.getByVideoIdList(videoIdList);
//            List<TaskTypeSampleEntity> insertList = new ArrayList<>();
//            videoIdList.forEach(eachVideoId -> {
//                if (!sampleIdList.contains(eachVideoId)) {
//                    SampleListResp resp = map.get(eachVideoId);
//
//                    String muAiType = resp.getMuAiType();
//                    String type = resp.getTaskTypeCode();
//                    // 保存保存样本
//                    TaskTypeSampleEntity insertVo = new TaskTypeSampleEntity();
//                    insertVo.setMuTypeId(muAiType);
//                    insertVo.setStatus(SampleEnum.PROCESSING.getStatus());
//                    insertVo.setTypeId(type);
//                    insertVo.setVideoId(eachVideoId);
//                    insertVo.setOrgId(orgCode);
//                    insertVo.setTotal(0);
//                    insertList.add(insertVo);
//                }
//            });
//            if (CollectionUtil.isNotEmpty(insertList)) {
//                taskTypeSampleService.saveBatch(insertList);
//            }
            List<SampleListResp> resultList = taskTypeSampleMapper.getListByVideoIdList(videoIdList, status, muTypeId);
            return resultList;
        }
        return new ArrayList<>();
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean start(List<String> videoCodeList) {
        // 修改状态
        taskTypeSampleMapper.updateByVideoIdList(videoCodeList, SampleEnum.PROCESSING.getStatus());

        //tellAiService.saveCacheByVideoIdList(videoIdList, RailWayMethodEnum.DEL_CACHE);

        // 属于哪些任务
        List<String> taskCodeList = taskTypeSampleMapper.getTaskIdList(videoCodeList);
        for (String taskCode : taskCodeList) {
            taskService.resetRedisTask(taskCode);
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean stop(List<String> videoCodeList) {
        // 修改状态
        taskTypeSampleMapper.updateByVideoIdList(videoCodeList, SampleEnum.END.getStatus());
        // 属于哪些任务
        List<String> taskCodeList = taskTypeSampleMapper.getTaskIdList(videoCodeList);
        // 保存缓存===>ai
        String videos = videoCodeList.stream().collect(Collectors.joining(","));
        tellAiService.toAi(videos, RailWayMethodEnum.SAVE_CACHE);
        for (String taskCode : taskCodeList) {
            taskService.resetRedisTask(taskCode);
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean restart(List<String> videoCodeList) {
        // 修改状态
        taskTypeSampleMapper.updateByVideoIdListRestart(videoCodeList, SampleEnum.PROCESSING.getStatus());
        // 属于哪些任务
        List<String> taskCodeList = taskTypeSampleMapper.getTaskIdList(videoCodeList);
        // 样本库清空
        taskTypeSampleMapper.emptyTotal(videoCodeList);
        for (String taskCode : taskCodeList) {
            taskService.resetRedisTask(taskCode);
        }
        // 删除样本库
        String videos = videoCodeList.stream().collect(Collectors.joining(","));
        tellAiService.toAi(videos, RailWayMethodEnum.DEL_DB);
        return true;
    }

    @Override
    public void clearByUser(String authenticationId) {
        //清除t_sample_library及t_sample_library_photo
        sampleLibraryMapper.deleteByUserId(authenticationId);
        //t_sample_library_feedback_photo及t_sample_library_feedback_photo_coordinate
        sampleLibraryFeedbackPhotoService.deleteByUserId(authenticationId);
        //打包清理t_sample_library_video--t_sample_match_photo--t_sample_user--t_sample_user_face--t_sample_user_photo--t_sample_user_photo_video
        sampleLibraryMapper.deleteByUserIdAllOther(authenticationId);

    }
}
