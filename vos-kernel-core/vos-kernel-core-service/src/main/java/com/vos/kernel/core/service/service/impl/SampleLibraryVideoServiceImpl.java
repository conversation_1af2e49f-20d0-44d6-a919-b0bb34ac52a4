package com.vos.kernel.core.service.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.vos.kernel.core.api.domain.SampleLibraryVideo;
import com.vos.kernel.core.api.rpc.SampleLibraryVideoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.vos.kernel.core.service.mapper.SampleLibraryVideoMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 设备正反向库关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
@Transactional(rollbackFor = Exception.class)
@Service
public class SampleLibraryVideoServiceImpl extends ServiceImpl<SampleLibraryVideoMapper, SampleLibraryVideo> implements SampleLibraryVideoService {

    /**
     * @description: 列表
     * @param:
     * @author: he<PERSON><PERSON><PERSON>
     * @date: 2022/5/6 15:49
     * @return:
     */
    @Override
    public List<SampleLibraryVideo> listTSampleLibraryVideo(String orgId, String videoId, String taskTypeId, Long sampleLibraryPhotoId) {
        LambdaQueryWrapper<SampleLibraryVideo> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotBlank(orgId)) {
            queryWrapper.eq(SampleLibraryVideo::getOrgId, orgId);
        }
        if (ObjectUtil.isNotEmpty(videoId)) {
            queryWrapper.eq(SampleLibraryVideo::getVideoId, videoId);
        }
        if (ObjectUtil.isNotEmpty(taskTypeId)) {
            queryWrapper.eq(SampleLibraryVideo::getTaskTypeId, taskTypeId);
        }
        if (ObjectUtil.isNotEmpty(sampleLibraryPhotoId)) {
            queryWrapper.eq(SampleLibraryVideo::getSampleLibraryPhotoId, sampleLibraryPhotoId);
        }
        return list(queryWrapper);
    }
}
