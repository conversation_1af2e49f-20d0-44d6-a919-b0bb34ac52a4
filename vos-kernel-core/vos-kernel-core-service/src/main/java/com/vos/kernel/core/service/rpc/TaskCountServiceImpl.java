package com.vos.kernel.core.service.rpc;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.vos.kernel.common.enums.AiExecutionEnum;
import com.vos.kernel.core.api.domain.TaskCountEntity;
import com.vos.kernel.core.service.mapper.TaskCountMapper;
import com.vos.kernel.core.api.rpc.ITaskCountService;
import com.vos.kernel.common.constant.RedisKey;
import com.xxl.job.core.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Service
@DubboService
public class TaskCountServiceImpl extends ServiceImpl<TaskCountMapper, TaskCountEntity> implements ITaskCountService {

    @Resource
    TaskCountMapper taskCountMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 统计数据获取
     *
     * @param pattern
     * @return
     */
    public Set<String> scanKeys(String pattern) {
        return stringRedisTemplate.execute((RedisCallback<Set<String>>) connection -> {
            Set<String> keys = new HashSet<>();
            Cursor<byte[]> cursor = connection.scan(ScanOptions.scanOptions().match(pattern).count(100).build());
            while (cursor.hasNext()) {
                keys.add(new String(cursor.next()));
            }
            return keys;
        });
    }

    @Async("callBackThreadPool")
    @Override
    public Object updateTotal(String orgCode, String videoCode, String taskCode, Integer muTypeId, String taskTypeCode, LocalDateTime now, Boolean isAiSuccess, Integer atomNum, Integer aiExecution) {
        if (aiExecution.equals(AiExecutionEnum.ONCE.getValue())) {
            taskCode = "TaskCodeApi";
            videoCode = "VideoCodeApi";
        }
        log.debug("统计执行参数orgCode：{},videoCode:{},taskCode:{},muTypeId:{},taskTypeCode:{},isAiSuccess:{},atomNum:{},aiExecution:{}", orgCode, videoCode, taskCode, muTypeId, taskTypeCode, isAiSuccess, atomNum, aiExecution);
        int hour = now.getHour();
        LocalDate localDate = now.toLocalDate();
        String key = RedisKey.TASK_TOTAL_KEY + orgCode + ":" + taskCode + ":" + videoCode + ":" + taskTypeCode + ":" + muTypeId + ":" + localDate + ":" + hour;
        if (isAiSuccess) {
            stringRedisTemplate.opsForHash().increment(key, "total", atomNum);
            stringRedisTemplate.opsForHash().increment(key, "aiSuccessNum", atomNum);

            //ai效果识别
            if (aiExecution.equals(AiExecutionEnum.ONCE.getValue())) {
                stringRedisTemplate.opsForHash().increment(key, "aiExecutionNum", atomNum);
            }
            //任务执行
            if (aiExecution.equals(AiExecutionEnum.RUNTIME.getValue())) {
                stringRedisTemplate.opsForHash().increment(key, "runTimeExecutionNum", atomNum);
            }
            //虚拟任务
            if (aiExecution.equals(AiExecutionEnum.API.getValue())) {
                stringRedisTemplate.opsForHash().increment(key, "apiExecutionNum", atomNum);
            }
            //v3
            if (aiExecution.equals(AiExecutionEnum.V3.getValue())) {
                stringRedisTemplate.opsForHash().increment(key, "v3ExecutionNum", atomNum);
            }
        } else {
            stringRedisTemplate.opsForHash().increment(key, "aiFailNum", atomNum);
        }
        log.debug("=====>开始缓存小时统计任务数{},模型数:{}", key, atomNum);
        return null;
    }

    @Override
    public void insertOrUpdateBatch(List<TaskCountEntity> taskCountList) {
        taskCountMapper.insertOrUpdateBatch(taskCountList);
    }

    @Override
    public Object getCount() {
        //task:total:count:TUserfhgz5n2uj93:TASKmxal1vfckvd:VID38cpqhw0bgr:TaskTypefw8ok4el1s51:20:2023-04-27:9
        String keyCount = RedisKey.TASK_TOTAL_KEY + "*" + ":" + "*" + ":" + "*" + ":" + "*" + ":" + "*" + ":" + "*" + ":" + "*";
        Set<String> keys = scanKeys(keyCount);
        log.debug("获取统计rediskey{},{}", JSONObject.toJSONString(keys));
        if (keys.isEmpty()) {
            return null;
        }
        LocalDate localDate = LocalDate.now();
        List<TaskCountEntity> taskCountList = new ArrayList<>();
        keys.stream().forEach(key -> {
            Map<Object, Object> map = stringRedisTemplate.opsForHash().entries(key);
            String[] split = key.split(":");
            TaskCountEntity taskCount = new TaskCountEntity();
            LocalDate ymd = LocalDate.parse(split[8]);
            taskCount.setOrgCode(split[3]);
            taskCount.setTaskCode(split[4]);
            taskCount.setVideoCode(split[5]);
            taskCount.setTaskTypeCode(split[6]);
            taskCount.setMuTypeId(Integer.valueOf(split[7]));
            taskCount.setYmd(ymd);
            taskCount.setH(Integer.valueOf(split[9]));

            taskCount.setTotal(Long.valueOf(Optional.ofNullable(map.get("total")).orElse(0).toString()));
            taskCount.setAiSuccessNum(Long.valueOf(Optional.ofNullable(map.get("aiSuccessNum")).orElse(0).toString()));
            taskCount.setAiExecutionNum(Long.valueOf(Optional.ofNullable(map.get("aiExecutionNum")).orElse(0).toString()));
            taskCount.setApiExecutionNum(Long.valueOf(Optional.ofNullable(map.get("apiExecutionNum")).orElse(0).toString()));
            taskCount.setRunTimeExecutionNum(Long.valueOf(Optional.ofNullable(map.get("runTimeExecutionNum")).orElse(0).toString()));
            taskCount.setV3ExecutionNum(Long.valueOf(Optional.ofNullable(map.get("v3ExecutionNum")).orElse(0).toString()));
            taskCount.setAiFailNum(Long.valueOf(Optional.ofNullable(map.get("aiFailNum")).orElse(0).toString()));

            taskCount.setCreateTime(DateUtil.addMinutes(new Date(),-1));
            taskCount.setUpdateTime(DateUtil.addMinutes(new Date(),-1));
            taskCountList.add(taskCount);
            if (localDate.isAfter(ymd)) {
                stringRedisTemplate.delete(key);
            }
        });
        List<List<TaskCountEntity>> countLists = Lists.partition(taskCountList, 1000);
        for (List<TaskCountEntity> taskCounts : countLists) {
            this.insertOrUpdateBatch(taskCounts);
        }
        return null;
    }
}
