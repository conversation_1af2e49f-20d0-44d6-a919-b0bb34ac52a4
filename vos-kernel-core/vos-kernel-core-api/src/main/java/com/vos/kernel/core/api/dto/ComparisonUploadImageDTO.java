package com.vos.kernel.core.api.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;

@Data
public class ComparisonUploadImageDTO implements Serializable {
    /**
     * groupId
     */
    private String indexId;

    /**
     * 样本地址
     */
    private String data;


    /**
     * 图片形式
     */
    private String srcType;

    /**
     * orgId
     */
    private String orgId;

    /**
     * videoId
     */
    private String videoId;

    /**
     * imageId
     */
    private String imageId;

    /**
     * imageId
     */
    private String taskId;


    /**
     * 额外参数
     * {
     *     "comparisonId":"1"
     * }
     */
    private JSONObject kwargs;

}
