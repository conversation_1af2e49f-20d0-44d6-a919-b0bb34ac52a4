package com.vos.kernel.core.api.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 反馈样本图片
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("t_sample_library_feedback_photo")
@ApiModel(value="SampleLibraryFeedbackPhoto对象", description="反馈样本图片")
public class SampleLibraryFeedbackPhoto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "feedback_photo_id", type = IdType.AUTO)
    private Long feedbackPhotoId;

    @ApiModelProperty(value = "机构id")
    private String orgId;

    @ApiModelProperty(value = "设备id")
    private Long videoId;

    @ApiModelProperty
    private String videoCode;

    @ApiModelProperty(value = "算子id")
    private Long taskTypeId;

    @ApiModelProperty
    private String taskTypeCode;
    @ApiModelProperty
    private Integer muTypeId;

    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty
    private String taskCode;

    @ApiModelProperty(value = "告警结果id")
    private Long resultId;

    @ApiModelProperty(value = "原图地址")
    private String originalImage;

    @ApiModelProperty(value = "图片路径")
    private String samplePath;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "删除标记 0:未删除 1：已删除")
    private Integer isDel;

    @ApiModelProperty(value = "bBoxes")
    private String bBoxes;

    @ApiModelProperty(value = "告警水印信息")
    private String waterPrint;

//    @ApiModelProperty(value = "色彩框类型：1是告警，2是未告警")
//    private Integer colorFlag;
    /**
     * 设备id
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @TableField(exist = false)
    private Date eventTime;

    @TableField(exist = false)
    private String taskName;

    @TableField(exist = false)
    private String videoName;

    @TableField(exist = false)
    private String taskTypeName;

    @ApiModelProperty(value = "配置标识")
    private String configCode;

    @ApiModelProperty
    private Integer goodStatus;
}
