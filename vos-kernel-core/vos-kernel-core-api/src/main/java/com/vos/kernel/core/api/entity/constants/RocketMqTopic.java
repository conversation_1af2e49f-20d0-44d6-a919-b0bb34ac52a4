package com.vos.kernel.core.api.entity.constants;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/29
 * @description: com.vos.kernel.core.api.entity.constants
 */
public class RocketMqTopic {

    /**
     * AI告警信息 api 输出
     */
    public static final String API_CALL_TOPIC = "alarm-api-call";

    /**
     * api调用返回
     */
    public static final String API_BACK_TOPIC = "api-back";

    /**
     * ai调用重试
     */
    public static final String AI_RETRY_TOPIC = "ai-retry";

    /**
     * 抽帧异步落盘
     */
    public static final String IMAGE_ASYNC_TOPIC = "cut-img-upload";

    /**
     * 告警异步落盘
     */
    public static final String ALARM_IMAGE_ASYNC_TOPIC = "alarm-img-upload";

    /**
     * om包安装回调返回
     */
    public static final String OM_CALL_BACK_TOPIC = "om-call-back";

    /**
     * om包安装回调返回
     */
    public static final String VIDEO_CALL_BACK_TOPIC = "video-call-back";

    /**
     * studio回流
     */
    public static final String STUDIO_REFLUX_TOPIC = "studio-kernel-reflux";


    public static final String V35_CALLBACK_TOPIC = "v35-multi-callback-topic";

    public static final String V3_CALLBACK_TOPIC = "v3-multi-callback-topic";


    /**
     * 用量推送
     */
    public static final String CMS_STATISTICS_TOPIC = "cms_statistics_topic";
}
