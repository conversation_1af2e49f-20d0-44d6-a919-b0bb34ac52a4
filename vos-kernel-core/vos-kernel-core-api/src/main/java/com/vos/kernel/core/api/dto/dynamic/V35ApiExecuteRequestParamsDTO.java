package com.vos.kernel.core.api.dto.dynamic;

import com.alibaba.fastjson.JSONObject;
import com.vos.kernel.core.api.dto.AiExecuteImageParamDTO;
import com.vos.kernel.core.api.dto.params.AiSelectRegionsParamsDTO;
import com.vos.kernel.core.api.dto.params.OmModelv35DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: 该类的定义说明、使用范围
 * @Author: whz
 * @Date: Created in 2023/10/26 15:03
 * @Version:产品版本号
 */
@Data
public class V35ApiExecuteRequestParamsDTO extends ApiExecuteParentParamsDTO {
    /**
     * 选择识别区域
     */
    private AiSelectRegionsParamsDTO selectedRegions;

    /**
     * 对话内容
     */
    @ApiModelProperty(value = " 对话内容")
    private OmModelv35DTO omModelv35DTO;

    /**
     * 参数：目前用于累计下发
     */
    private List<AiExecuteImageParamDTO> executeImageParams;

    /**
     * 置信度
     */
    private Float threshold;

    private JSONObject model;

    public String getUniqueKey() {
        return getAppId() + getOperator() + getAbilityId() + getCallBackUrl();
    }

}
