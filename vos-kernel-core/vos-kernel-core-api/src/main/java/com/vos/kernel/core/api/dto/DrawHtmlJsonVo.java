package com.vos.kernel.core.api.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * htmlJson
 */
@Data
@Accessors(chain = true)
public class DrawHtmlJsonVo implements Serializable {

    /**
     * 点位x1,y1,x2,y2
     */
    private int[] bbox;

    /**
     * 置信度
     */
    private List<String> conf;

    /**
     * 标签
     */
    private List<String> label;

    /**
     * 颜色,rgba
     */
    private int[] color;

    /**
     * 状态 1：点赞 2：点踩
     */
    private Integer goodStatus;

    private boolean matched;

    private boolean status;
}
