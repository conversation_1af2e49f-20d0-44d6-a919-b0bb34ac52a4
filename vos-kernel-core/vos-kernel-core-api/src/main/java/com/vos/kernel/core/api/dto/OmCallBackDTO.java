package com.vos.kernel.core.api.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/9
 * @description: com.vos.kernel.core.api.dto
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OmCallBackDTO implements Serializable {
    /**
     * 应用唯一键
     */
    private String taskTypeCode;

    /**
     * 应用状态
     */
    private Integer appStatus;

    /**
     * 行为编号
     */
    private String actionId;

    /**
     * 算法请求地址
     */
    private String abilityUrl;

    /**
     * 从节点ip
     */
    private String slaveIp;

    /**
     * 从节点
     */
    private String slave;

    /**
     * 异常信息
     */
    private String failedMessage;

    /**
     * 是否比对算法
     */
    private Boolean isMatchType;
    /**
     * 比对算法请求url
     */
    private String matchUploadUrl;
}
