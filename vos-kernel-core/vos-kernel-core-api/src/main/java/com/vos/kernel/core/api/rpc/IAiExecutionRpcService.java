package com.vos.kernel.core.api.rpc;

import com.alibaba.fastjson.JSONObject;
import com.linker.omos.client.domain.workflow.callback.TaskCallBackDto;
import com.vos.kernel.core.api.dto.*;
import com.vos.kernel.core.api.dto.dynamic.ApiExecuteParentParamsDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/14
 * @description: com.vos.kernel.core.api.rpc
 */
public interface IAiExecutionRpcService {

    /**
     * 预览ai能力效果
     *
     * @param aiPreViewCallDTO
     */
    AiCallFinalReturnDTO executeAiOnce(AiPreViewCallDTO aiPreViewCallDTO);

    /**
     * 任务运行时调用
     *
     * @param abilityTaskCall
     */
    void runTimeExecute(AiAbilityTaskCallDTO abilityTaskCall);

    /**
     * API形式调用多个虚拟任务中的ai能力
     *
     * @param abilityTaskCall
     */
    void apiExecute(ApiAbilityTaskCallDTO abilityTaskCall);

    /**
     * v3 api 能力调用
     *
     * @param v3ApiExecuteParams
     */
    void v3ApiExecute(V3ApiExecuteParamsDTO v3ApiExecuteParams);

    /**
     * v3.5 api 能力调用
     *
     * @param v35ApiExecuteParamsDTO
     */
    void v35ApiExecute(V35ApiExecuteParamsDTO v35ApiExecuteParamsDTO);

    /**
     * 任务下发回调处理
     *
     * @param taskCallBackDto
     */
    void taskCallBackHandler(TaskCallBackDto taskCallBackDto);

    /**
     * 视频流类应用任务下发
     */
    void videoAbilityTaskProcess(JSONObject jsonObject);

    /**
     * 视频流类应用任务启停控制
     */
    void videoAbilityTaskControl(JSONObject jsonObject);

    void dynamicV2ApiExecute(ApiAbilityTaskCallDTO apiAbilityTaskCallDTO, String abilityTemplate);

    void dynamicApiExecute(ApiExecuteParentParamsDTO apiExecuteParentParamsDTO, String template, String type);

    Long getImageListSize(String appSourceId);
}
