<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.vos.kernel</groupId>
        <artifactId>vos-kernel</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.vos.task.poll</groupId>
    <artifactId>vos-task-poll</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>


    <modules>
        <module>vos-task-poll-service</module>
        <module>vos-task-poll-api</module>
    </modules>

    <properties>
        <pool.version>1.0.0-SNAPSHOT</pool.version>
        <manage.version>1.0.0-SNAPSHOT</manage.version>
        <kernel-common.version>1.0.0-SNAPSHOT</kernel-common.version>
    </properties>

    <dependencies>

        <!-- WebClient是从Spring WebFlux 5.0版本开始提供的一个非阻塞的基于响应式编程的进行Http请求的客户端工具 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>

    </dependencies>

    <dependencyManagement>

        <dependencies>
            <dependency>
                <groupId>com.vos.task.poll</groupId>
                <artifactId>vos-task-poll-api</artifactId>
                <version>${pool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.vos.task.poll</groupId>
                <artifactId>vos-task-poll-service</artifactId>
                <version>${pool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.vos.task.manage</groupId>
                <artifactId>vos-task-manage-api</artifactId>
                <version>${manage.version}</version>
            </dependency>

        </dependencies>

    </dependencyManagement>

</project>
