package com.vos.task.poll.api.entity.dto;

import com.vos.task.automated.api.model.dto.k8s.GpuItemDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年10月08日
 * @version: 1.0
 * @description: TODO
 */
@Data
public class ServingInfoDto implements Serializable {

    /**
     * serving标志
     */
    private String servingId;


    /**
     * 集群状态
     */
    private String servingStatus;


    /**
     * 水位信息
     */
    private List<AbilityWaterInfoDto> servingInfo;

    /**
     * 总gpu
     */
    private String totalGpu;

    /**
     * 使用gpu
     */
    private String useGpu;

    /**
     * 主机下卡信息
     */
    private List<GpuItemDTO> detail;


}
