package com.vos.task.poll.api.entity.model;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableMap;
import com.vos.kernel.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 任务-通道信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("lh_task_queue_info")
public class TaskQueueInfo extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 主键
    */
    @TableId(value = "info_id", type = IdType.AUTO)
    private Long infoId;

    /**
    * 应用ID
    */
    private Long appId;

    /**
    * 能力ID
    */
    private Long abilityId;

    /**
    * mq主题名称
    */
    private String topicName;

    /**
    * mq生产者组
    */
    private String producerGroup;

    /**
    * mq消费者组
    */
    private String consumerGroup;

    /**
    * mq执行排序
    */
    private Integer taskOrder;

    /**
    * 对应的插队的redis队列名称
    */
    private String redisQueueName;

    /**
    * 轮询的线程名称
    */
    private String pollThreadName;

    @TableField(exist = false)
    private List<Long> abilityIds;
    @TableField(exist = false)
    private Map<String, String> header;
    @TableField(exist = false)
    private Map<String, String> body;

    /**
     * 编码放进队列
     * @param topic
     * @return
     */
    public String encode(String topic) {
        // topic为null只存在于redis队列的情况
        if (StringUtils.isEmpty(topic)) {
            topic = "";
        }
        // 组装消息协议头
        ImmutableMap.Builder headerBuilder = new ImmutableMap.Builder<String, String>()
                .put("topicName", topic);
        header = headerBuilder.build();

        body = new ImmutableMap.Builder<String, String>()
                .put("appId", String.valueOf(this.getAppId()))
                .put("abilityIds", JSON.toJSONString(this.getAbilityIds()))
                .build();

        ImmutableMap<String, Object> map = new ImmutableMap.Builder<String, Object>()
                .put("header", header)
                .put("body", body)
                .build();

        // 返回序列化消息Json串
        String result = null;
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            result = objectMapper.writeValueAsString(map);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("消息序列化json异常", e);
        }
        return result;
    }

    /**
     * 解码从队列中取出
     * @param msg
     */
    public void decode(String msg) {
        Preconditions.checkNotNull(msg);
        ObjectMapper mapper = new ObjectMapper();
        try {
            JsonNode root = mapper.readTree(msg);
            // header
            this.setTopicName(root.get("header").get("topicName").asText());
            // body
            this.setAppId(root.get("body").get("appId").asLong());
            this.setAbilityIds(JSON.parseArray(root.get("body").get("abilityIds").asText(), Long.class));
        } catch (IOException e) {
            throw new RuntimeException("消息反序列化异常", e);
        }
    }
}
