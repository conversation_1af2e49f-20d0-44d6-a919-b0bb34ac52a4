package com.vos.task.poll.service.handler.ability;

import com.vos.task.automated.api.model.dto.AbilityRequestUrlDTO;
import com.vos.task.poll.service.handler.AbilityHttpConcurrentLimit;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.sse.EventSource;
import org.apache.dubbo.common.stream.StreamObserver;
import reactor.core.Disposable;

/**
 * <AUTHOR>
 * @date 2024年05月08日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
public class SseSimpleStreamObserver extends SimpleStreamObserver {

    EventSource eventSource;


    public SseSimpleStreamObserver(AbilityHttpConcurrentLimit abilityHttpConcurrentLimit, AbilityRequestUrlDTO abilityRequestInfo) {
        super(abilityHttpConcurrentLimit, abilityRequestInfo);
    }

    @Override
    public void onNext(String data) {
        log.info("stream <- reply:{}", data);
    }

    @Override
    public void onError(Throwable throwable) {
        log.info("poll stream error", throwable);
        if (s != null && !s.isDisposed()) {
            log.info("chat 客户端异常取消请求");
            s.dispose();
        }
        if (this.eventSource != null) {
            log.info("chat 客户端异常取消请求");
            this.eventSource.cancel();
        }
        //释放资源
        release("", 0);
    }

    /**
     * 释放资源
     */
    public synchronized void release(String appSourceId, long l) {
        if (null != abilityHttpConcurrentLimit && abilityRequestInfo != null) {
            abilityHttpConcurrentLimit.release(abilityRequestInfo);
            if (l > 0) {
                abilityHttpConcurrentLimit.addRt(appSourceId, abilityRequestInfo.getRouterId(), 1, l, 0);
            }
            abilityRequestInfo = null;
        }

    }

    @Override
    public void onCompleted() {
        log.info("stream completed");
        if (this.eventSource != null) {
            this.eventSource.cancel();
        }
    }

    public EventSource getEventSource() {
        return eventSource;
    }

    public void setEventSource(EventSource eventSource) {
        this.eventSource = eventSource;
    }
}
