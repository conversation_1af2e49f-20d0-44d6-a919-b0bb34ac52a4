package com.vos.task.poll.service.config;

import cn.hutool.core.util.StrUtil;
import com.alibaba.cloud.nacos.NacosConfigProperties;
import com.alibaba.nacos.client.naming.NacosNamingService;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

/**
 * 获取nacos中服务注册工具
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/14
 * @description: com.vos.task.poll.service.schedule
 */
@Configuration
@AllArgsConstructor
@Slf4j
public class NacosNamingServiceConfig {

    @SneakyThrows
    @Bean
    public NacosNamingService nacosNamingService(NacosConfigProperties nacosConfigProperties) {
        Properties properties = new Properties();
        properties.setProperty("serverAddr", nacosConfigProperties.getServerAddr());
        properties.setProperty("namespace", nacosConfigProperties.getNamespace());
        if (StrUtil.isNotBlank(nacosConfigProperties.getUsername())) {
            properties.setProperty("username", nacosConfigProperties.getUsername());
            properties.setProperty("password", nacosConfigProperties.getPassword());
        }
        return new NacosNamingService(properties);
    }

}