package com.vos.task.poll.service.schedule;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.linker.omos.client.domain.CommonConstant;
import com.vos.kernel.common.entity.R;
import com.vos.task.automated.api.model.entity.SlaveEntity;
import com.vos.task.poll.service.handler.IMessageQueueOffSetService;
import com.vos.task.poll.service.rocketmq.RocketMqManageToolServiceImpl;
import com.vos.task.poll.service.service.TaskQueueInfoService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.admin.TopicOffset;
import org.apache.rocketmq.common.admin.TopicStatsTable;
import org.apache.rocketmq.common.message.MessageQueue;
import org.apache.skywalking.apm.toolkit.trace.SupplierWrapper;
import org.dromara.dynamictp.core.executor.DtpExecutor;
import org.springframework.data.redis.connection.StringRedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年10月30日
 * @version: 1.0
 * @description: 按比例分配算法队列最大偏移量获取缓存任务
 */
@Slf4j
@Component
public class AbilityRatioQueueOffSetTask {

    @Resource
    TaskQueueInfoService taskQueueInfoService;

    @Resource
    IMessageQueueOffSetService messageQueueOffSetService;

    @Resource
    RocketMqManageToolServiceImpl rocketMqManageToolService;

    @Resource
    DtpExecutor ratioOffSetThreadPool;


    @XxlJob(value = "getAbilityRatioQueueOffSet")
    public ReturnT<String> getAbilityRatioQueueOffSet() {

        int shardTotal = XxlJobHelper.getShardTotal();
        int shardIndex = XxlJobHelper.getShardIndex();

        List<String> allAbilityTopic = taskQueueInfoService.getAllAbilityTopic("all");
        allAbilityTopic = allAbilityTopic.stream().filter(t -> t.contains("RATIO_")).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(allAbilityTopic)) {
            return ReturnT.SUCCESS;
        }
        int i = 0;

        ArrayList<String> nodeAbilityTopic = new ArrayList<>();

        for (String ratioTopic : allAbilityTopic) {
            if (i % shardTotal == shardIndex) {
                nodeAbilityTopic.add(ratioTopic);
            }
            i++;
        }

        log.debug("shardTotal:{},shardIndex:{},nodeAbilityTopic:{}", shardTotal, shardIndex, nodeAbilityTopic);
        List<CompletableFuture<Void>> collect = nodeAbilityTopic.stream().map(ratioTopic -> CompletableFuture.runAsync(() -> getAbilityMaxOffset(ratioTopic), ratioOffSetThreadPool)).collect(Collectors.toList());
        collect.stream().map(CompletableFuture::join).collect(Collectors.toList());

        return ReturnT.SUCCESS;
    }


    /**
     * 获取rocketMq生产最大值
     *
     * @param topicName
     * @return
     */
    public void getAbilityMaxOffset(String topicName) {

        try {
            TopicStatsTable topicStatsTable = rocketMqManageToolService.examineTopicStats(topicName);
            HashMap<MessageQueue, TopicOffset> offsetTable = topicStatsTable.getOffsetTable();
            HashMap<String, String> abilityMaxOffsetMap = new HashMap<>(16);
            for (Map.Entry<MessageQueue, TopicOffset> m : offsetTable.entrySet()) {
                TopicOffset value = m.getValue();
                MessageQueue key = m.getKey();
                if (value != null && value.getMaxOffset() > 0) {
                    String queueName = topicName + "_" + key.getQueueId() + ":" + key.getBrokerName();
                    abilityMaxOffsetMap.put(queueName, value.getMaxOffset() + "");
                }
            }
            if (MapUtil.isNotEmpty(abilityMaxOffsetMap)) {
                messageQueueOffSetService.setRatioAbilityMaxOffset(abilityMaxOffsetMap);
            }
        } catch (Exception e) {
            log.error("获取真实消费进度信息失败", e);
        }
    }

}
