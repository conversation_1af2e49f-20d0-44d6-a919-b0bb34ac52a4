package com.vos.task.poll.service.controller;

import com.linker.basic.baseclass.BaseResp;
import com.vos.task.poll.api.entity.dto.ClusterInfoDto;
import com.vos.task.poll.api.service.rpc.PollRpcService;
import com.vos.task.poll.api.service.rpc.TaskQueueInfoPrcService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/check")
public class PollApiController {

    @Resource
    private TaskQueueInfoPrcService taskQueueInfoPrcService;

    @Resource
    PollRpcService pollRpcService;

    /**
     * 查询通道阻塞信息
     *
     * @param isAllFlag 是否查询全部队列  0 查询全部  1 查询 mq通道阻塞量大于0的 队列信息
     * @return R 通用返回体
     */
    @GetMapping("/getQueueInfo")
    public BaseResp getQueueInfo(@RequestParam(required = false) Integer isAllFlag) throws Exception {
        return new BaseResp(taskQueueInfoPrcService.getQueueInfo(isAllFlag));
    }

    /**
     * 跳过指定通道信息
     *
     * @return R 通用返回体
     */
    @GetMapping("/jumpQueue")
    public BaseResp jumpQueue(@RequestParam String queueId, @RequestParam String topic) throws Exception {
        return new BaseResp(taskQueueInfoPrcService.jumpQueue(queueId, topic));
    }

    /**
     * 跳过所有通道信息
     *
     * @return R 通用返回体
     */
    @GetMapping("/jumpAllQueue")
    public BaseResp jumpAllQueue(@RequestParam(value = "isAllFlag", required = false) Integer isAllFlag) throws Exception {
        return new BaseResp(taskQueueInfoPrcService.jumpAllQueue(isAllFlag));
    }

    @GetMapping("/getServingInfo")
    public BaseResp getServingInfo() throws Exception {
        return new BaseResp(pollRpcService.getClusterInfo());
    }
}