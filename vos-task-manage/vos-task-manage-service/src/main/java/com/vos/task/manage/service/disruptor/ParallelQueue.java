package com.vos.task.manage.service.disruptor;

/**
 * <AUTHOR>
 * @date 2024年09月10日
 * @version: 1.0
 * @description: 多生产者多消费者处理接口
 */
public interface ParallelQueue<E> {

    /**
     * 添加元素
     *
     * @param event
     */
    void add(E event);

    /**
     * 添加多个元素
     *
     * @param event
     */
    void add(E... event);

    /**
     * 添加多个元素
     *
     * @param event
     * @return
     */
    boolean tryAdd(E event);

    /**
     * 添加多个元素
     *
     * @param event
     * @return
     */
    boolean tryAdd(E... event);

    /**
     * 启动
     */
    void start();

    /**
     * 销毁
     */
    void shutDown();

    /**
     * 判断是否已经销毁
     */
    boolean isShutDown();

}
