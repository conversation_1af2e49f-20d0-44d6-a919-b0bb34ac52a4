package com.vos.task.manage.service.disruptor;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.linker.log.enums.MetricPattern;
import com.linker.log.utils.TraceLogUtil;
import com.linker.omos.client.config.ItemCodeEnum;
import com.lmax.disruptor.BlockingWaitStrategy;
import com.lmax.disruptor.dsl.ProducerType;
import com.vos.kernel.common.cache.ITaskCacheRpcService;
import com.vos.kernel.common.entity.TaskSub;
import com.vos.task.manage.service.component.loader.WebClientLoader;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.ConsumerWrapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2024年11月20日
 * @version: 1.0
 * @description: TODO
 */
@Slf4j
@Service
public class DisruptorTaskCallBack {

    @Resource
    WebClientLoader webClientLoader;

    @Resource
    ITaskCacheRpcService taskCacheRpcService;


    @Value("${statistic.schedule.persistSwitch:true}")
    private boolean persistSwitch;

    /**
     * 线程名称前缀
     */
    private static final String THREAD_NAME_PREFIX = "back-queue-";


    /**
     * disruptor处理器
     */
    private ParallelQueueHandler<DisruptorTaskCallBackDto> parallelQueueHandler;

    /**
     * 构造器
     */
    public DisruptorTaskCallBack() {
        ParallelQueueHandler.Builder<DisruptorTaskCallBackDto> builder = new ParallelQueueHandler.Builder<DisruptorTaskCallBackDto>()
                .setThreads(Runtime.getRuntime().availableProcessors())
                .setProducerType(ProducerType.MULTI)
                .setNamePrefix(THREAD_NAME_PREFIX)
                .setWaitStrategy(new BlockingWaitStrategy());

        BatchEventListenerProcessor batchEventListenerProcessor = new BatchEventListenerProcessor();
        builder.setListener(batchEventListenerProcessor);
        this.parallelQueueHandler = builder.build();
    }

    @PostConstruct
    public void init() {
        if (this.parallelQueueHandler != null) {
            this.parallelQueueHandler.start();
        }
    }

    @PreDestroy
    public void destroy() {
        if (this.parallelQueueHandler != null) {
            this.parallelQueueHandler.shutDown();
        }
    }


    /**
     * 批量事件监听
     */
    public class BatchEventListenerProcessor implements EventListener<DisruptorTaskCallBackDto> {

        @Override
        public void onEvent(DisruptorTaskCallBackDto event) {
            if (null != event) {
                callBackDo(event);
            }
        }

        @Override
        public void onException(Throwable ex, long sequence, DisruptorTaskCallBackDto event) {
            log.error("od&attrDet 请求写回失败，request:{},errMsg:{} ", event, ex.getMessage(), ex);
            callBackDo(event);
        }
    }

    /**
     * http回调
     *
     * @param taskCallBackDto
     */
    public void httpCallBack(DisruptorTaskCallBackDto taskCallBackDto) {
        if (this.parallelQueueHandler != null) {
            this.parallelQueueHandler.add(taskCallBackDto);
        } else {
            log.error("httpCallBack回调异常，parallelQueueHandler未初始化");
        }
    }

    private void callBackDo(DisruptorTaskCallBackDto event) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        TaskSub taskSub = event.getTaskSub();
        event.getCallBack().setMetaInfo(taskSub.getExtend());
        Long subTaskId = taskSub.getSubTaskId();
        AtomicInteger retryCount = new AtomicInteger(0);
        JSONObject errorReturn = new JSONObject();
        Mono<JSONObject> mono = webClientLoader.getRequestAbilityCallBackClient(taskSub.getAbilityId())
                .requestAbility(event.getUrl(), event.getCallBack())
                .doOnError(Exception.class, ConsumerWrapper.of(err -> {
                    log.error("subTaskId={},接口回调业务方发生错误={},重试次数={}", subTaskId, err.getMessage(), retryCount.getAndIncrement());
                    errorReturn.put("error", err.getMessage());
                })).onErrorReturn(new JSONObject());

        mono.subscribe(ConsumerWrapper.of(result -> {
            updateRequestBusinessResult(taskSub, result);
            stopWatch.stop();
            if (StrUtil.isNotBlank(taskSub.getExtend())) {
                JSONObject jsonObject = JSON.parseObject(taskSub.getExtend());
                String extra = jsonObject.getString("deviceId") + "," + jsonObject.getString("bizTaskId") + "," + jsonObject.getString("abilityCode");
                TraceLogUtil.trace(ItemCodeEnum.SF_P51YP4J99EV.getCode(), MetricPattern.CUSTOM, "", stopWatch.getTotalTimeMillis(), StrUtil.isBlank(errorReturn.getString("error")) ? "0" : "50", jsonObject.getLong("frameBizTime"),
                        StrUtil.isBlank(errorReturn.getString("error")) ? extra : extra + "," + errorReturn.getString("error"));
            }
        }));
    }

    /**
     * 业务方异步回调处理
     *
     * @param taskSub
     * @param callJson
     */
    protected void updateRequestBusinessResult(TaskSub taskSub, JSONObject callJson) {
        if (log.isDebugEnabled()) {
            log.debug("subTaskId={},回调业务方返回结果=【{}】", taskSub.getSubTaskId(), JSON.toJSONString(callJson));
        }
        // 任务转存
        Boolean result = this.taskDump(taskSub.getTaskId());
        if (log.isDebugEnabled()) {
            if (result) {
                log.debug("taskId={},subTaskId={},任务转存成功】", taskSub.getTaskId(), taskSub.getSubTaskId());
            } else {
                log.debug("taskId={},subTaskId={},任务转存失败】", taskSub.getTaskId(), taskSub.getSubTaskId());
            }
        }
    }

    /**
     * 任务转存
     *
     * @param taskId
     * @return
     */
    protected Boolean taskDump(Long taskId) {
        if (persistSwitch) {
            taskCacheRpcService.taskResultDumpCache(taskId.toString());
        }
        return taskCacheRpcService.deleteCache(taskId.toString());
    }
}
