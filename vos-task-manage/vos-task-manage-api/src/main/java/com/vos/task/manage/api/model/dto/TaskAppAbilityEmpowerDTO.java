package com.vos.task.manage.api.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 任务-应用能力赋权 dto
 *
 * <AUTHOR>
 * @since 2021-11-5 13:41:13
 */
@Data
public class TaskAppAbilityEmpowerDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 对应能力
     */
    @NotNull(message = "对应能力ID不能为空")
    private Long abilityId;

    /**
     * 支持的并发路数
     */
    private Integer concurrentLimit;

    /**
     * 是否为专线 0否 1是
     */
    private Integer isSpecial = 0;

    /**
     *  0 普通能力  1 大模型等特殊能力 默认0
     */
    private Integer abilityType = 0;
}