package com.vos.task.manage.api.model.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 任务-机器分页视图对象
 *
 * <AUTHOR>
 * @since 2021-07-20 13:41:13
 */
@Data
public class TaskServerVo {
    private static final long serialVersionUID = 1L;

    /**
     * 机器id
     */
    private Long serverId;
    
    /**
     * 机器ip
     */
    private String serverIp;
    
    /**
     * 机器名称
     */
    private String serverName;
    
    /**
     * 所在机房
     */
    private String sercerRoom;
    
    /**
     * 备注
     */
    private String serverRemark;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 删除标志
     */
    private Integer isDeleted;
    
}