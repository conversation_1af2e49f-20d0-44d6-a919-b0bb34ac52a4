package com.vos.task.manage.api.model.dto.task;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-1-23 13:47:56
 */
@Data
public class TaskNodeQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 关键词
     */
    private String keyWord;


    /**
     * 租户ID
     */
    private Long tenantId;


    /**
     * 应用appId
     */
    private Long appId;


    /**
     * 开始时间  格式 2021-12-20 14:58:55
     */
    private String startTime;


    /**
     * 结束时间  格式 2021-12-20 14:58:55
     */
    private String endTime;


    /**
     *  hz能力集合
     */
    private List<Long> abilityIds = new ArrayList<>();



    /**
     *  任务状态集合（除处理中的）
     */
    private List<Integer> taskStatusList = new ArrayList<>();


    /**
     * 处理中状态集合
     */
    private List<Integer> handleTaskStatusList = new ArrayList<>();



    /**
     * 组装sql
     */
    private String havingSql;







}